#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up a test PostgreSQL database for integration testing.
This script creates a test database and sets up the required tables.
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test database configuration
TEST_DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'postgres'),
    'password': os.environ.get('DB_PASSWORD', 'test_password'),
    'port': int(os.environ.get('DB_PORT', 5432))
}

TEST_DB_NAME = os.environ.get('DB_NAME', 'psychiatric_test_db')

def check_postgresql_connection():
    """Check if PostgreSQL is running and accessible"""
    try:
        conn = psycopg2.connect(
            host=TEST_DB_CONFIG['host'],
            user=TEST_DB_CONFIG['user'],
            password=TEST_DB_CONFIG['password'],
            port=TEST_DB_CONFIG['port'],
            database='postgres'  # Connect to default database
        )
        conn.close()
        logger.info("PostgreSQL connection successful")
        return True
    except psycopg2.OperationalError as e:
        logger.error(f"Cannot connect to PostgreSQL: {e}")
        return False

def create_test_database():
    """Create the test database if it doesn't exist"""
    try:
        # Connect to PostgreSQL server (not to a specific database)
        conn = psycopg2.connect(
            host=TEST_DB_CONFIG['host'],
            user=TEST_DB_CONFIG['user'],
            password=TEST_DB_CONFIG['password'],
            port=TEST_DB_CONFIG['port'],
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (TEST_DB_NAME,))
        exists = cursor.fetchone()
        
        if not exists:
            # Create the test database
            cursor.execute(f'CREATE DATABASE "{TEST_DB_NAME}"')
            logger.info(f"Created test database: {TEST_DB_NAME}")
        else:
            logger.info(f"Test database already exists: {TEST_DB_NAME}")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.Error as e:
        logger.error(f"Error creating test database: {e}")
        return False

def setup_test_tables():
    """Set up the required tables in the test database"""
    try:
        # Import the database module to use its table creation functionality
        from database import PsychiatricAssessmentDB
        
        # Set environment variables for test database
        os.environ['DB_HOST'] = TEST_DB_CONFIG['host']
        os.environ['DB_USER'] = TEST_DB_CONFIG['user']
        os.environ['DB_PASSWORD'] = TEST_DB_CONFIG['password']
        os.environ['DB_PORT'] = str(TEST_DB_CONFIG['port'])
        os.environ['DB_NAME'] = TEST_DB_NAME
        
        # Create database instance and tables
        db = PsychiatricAssessmentDB()
        db.create_tables()
        
        logger.info("Test database tables created successfully")
        
        # Test basic functionality
        health_status, health_message = db.health_check()
        if health_status:
            logger.info(f"Database health check passed: {health_message}")
        else:
            logger.warning(f"Database health check failed: {health_message}")
        
        db.close_connections()
        return True
        
    except Exception as e:
        logger.error(f"Error setting up test tables: {e}")
        return False

def cleanup_test_database():
    """Clean up the test database (optional)"""
    try:
        conn = psycopg2.connect(
            host=TEST_DB_CONFIG['host'],
            user=TEST_DB_CONFIG['user'],
            password=TEST_DB_CONFIG['password'],
            port=TEST_DB_CONFIG['port'],
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Terminate connections to the test database
        cursor.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = %s AND pid <> pg_backend_pid()
        """, (TEST_DB_NAME,))
        
        # Drop the test database
        cursor.execute(f'DROP DATABASE IF EXISTS "{TEST_DB_NAME}"')
        logger.info(f"Dropped test database: {TEST_DB_NAME}")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.Error as e:
        logger.error(f"Error cleaning up test database: {e}")
        return False

def main():
    """Main function to set up test database"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Set up PostgreSQL test database')
    parser.add_argument('--cleanup', action='store_true', help='Clean up test database instead of creating it')
    parser.add_argument('--check', action='store_true', help='Only check PostgreSQL connection')
    
    args = parser.parse_args()
    
    if args.check:
        if check_postgresql_connection():
            logger.info("PostgreSQL is accessible")
            sys.exit(0)
        else:
            logger.error("PostgreSQL is not accessible")
            sys.exit(1)
    
    if args.cleanup:
        if cleanup_test_database():
            logger.info("Test database cleanup completed")
            sys.exit(0)
        else:
            logger.error("Test database cleanup failed")
            sys.exit(1)
    
    # Default: Set up test database
    logger.info("Setting up test database...")
    
    # Step 1: Check PostgreSQL connection
    if not check_postgresql_connection():
        logger.error("Cannot connect to PostgreSQL. Please ensure PostgreSQL is running.")
        sys.exit(1)
    
    # Step 2: Create test database
    if not create_test_database():
        logger.error("Failed to create test database")
        sys.exit(1)
    
    # Step 3: Set up tables
    if not setup_test_tables():
        logger.error("Failed to set up test tables")
        sys.exit(1)
    
    logger.info("Test database setup completed successfully!")
    logger.info(f"Test database: {TEST_DB_NAME}")
    logger.info(f"Host: {TEST_DB_CONFIG['host']}:{TEST_DB_CONFIG['port']}")
    logger.info("You can now run integration tests with: python -m pytest tests/test_database_integration.py")

if __name__ == "__main__":
    main()
