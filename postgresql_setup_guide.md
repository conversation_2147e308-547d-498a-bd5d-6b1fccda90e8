# PostgreSQL Installation and Setup Guide

## Option 1: Manual Installation (Recommended)

### Step 1: Download PostgreSQL
1. Go to https://www.postgresql.org/download/windows/
2. Download PostgreSQL 15.8 (or latest stable version)
3. Run the installer as Administrator

### Step 2: Installation Configuration
During installation, configure:
- **Installation Directory**: `C:\Program Files\PostgreSQL\15`
- **Data Directory**: `C:\Program Files\PostgreSQL\15\data`
- **Port**: `5432` (default)
- **Superuser Password**: Choose a secure password (remember this!)
- **Locale**: Default

### Step 3: Post-Installation Setup
1. Add PostgreSQL to PATH:
   - Add `C:\Program Files\PostgreSQL\15\bin` to system PATH
2. Verify installation:
   ```powershell
   psql --version
   ```

### Step 4: Create Database and User
Open Command Prompt as Administrator and run:
```powershell
# Connect to PostgreSQL as superuser
psql -U postgres

# Create database
CREATE DATABASE psychiatric_assessments;

# Create user (optional - can use postgres user)
CREATE USER psych_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE psychiatric_assessments TO psych_user;

# Exit psql
\q
```

## Option 2: Using Docker (Alternative)

If you prefer Docker:
```powershell
# Pull PostgreSQL image
docker pull postgres:15

# Run PostgreSQL container
docker run --name postgres-psych -e POSTGRES_PASSWORD=password -e POSTGRES_DB=psychiatric_assessments -p 5432:5432 -d postgres:15
```

## Environment Variables Setup

Create a `.env` file in the project root:
```
DB_HOST=localhost
DB_NAME=psychiatric_assessments
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_PORT=5432
```

## Testing Connection

After installation, test the connection:
```powershell
# Test basic connection
psql -h localhost -U postgres -d psychiatric_assessments

# Test from Python
python -c "import psycopg2; conn = psycopg2.connect(host='localhost', database='psychiatric_assessments', user='postgres', password='your_password'); print('Connection successful!'); conn.close()"
```

## Next Steps

1. Install PostgreSQL using one of the methods above
2. Create the database and configure credentials
3. Update the application's database configuration
4. Run the database schema creation
5. Test the application in online mode

## Troubleshooting

### Common Issues:
- **Port 5432 already in use**: Check for existing PostgreSQL installations
- **Authentication failed**: Verify username/password
- **Connection refused**: Ensure PostgreSQL service is running

### Service Management:
```powershell
# Start PostgreSQL service
net start postgresql-x64-15

# Stop PostgreSQL service
net stop postgresql-x64-15

# Check service status
Get-Service -Name "*postgres*"
```
