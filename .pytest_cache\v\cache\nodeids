["tests/test_application_logic.py::TestAutoSaveFunctionality::test_enhanced_auto_save_already_saving", "tests/test_application_logic.py::TestAutoSaveFunctionality::test_enhanced_auto_save_no_changes", "tests/test_application_logic.py::TestAutoSaveFunctionality::test_enhanced_auto_save_offline_mode", "tests/test_application_logic.py::TestConnectionHealthChecking::test_check_connection_health_no_db", "tests/test_application_logic.py::TestConnectionHealthChecking::test_check_connection_health_with_db", "tests/test_application_logic.py::TestDataExportAndPreparation::test_prepare_export_data_basic", "tests/test_application_logic.py::TestDataExportAndPreparation::test_prepare_export_data_completion_percentage", "tests/test_application_logic.py::TestDataValidator::test_validate_field_empty_critical", "tests/test_application_logic.py::TestDataValidator::test_validate_field_empty_non_critical", "tests/test_application_logic.py::TestDataValidator::test_validate_field_invalid_age_negative", "tests/test_application_logic.py::TestDataValidator::test_validate_field_invalid_age_too_high", "tests/test_application_logic.py::TestDataValidator::test_validate_field_invalid_type", "tests/test_application_logic.py::TestDataValidator::test_validate_field_options_validation", "tests/test_application_logic.py::TestDataValidator::test_validate_field_string_length_warning", "tests/test_application_logic.py::TestDataValidator::test_validate_field_valid_age", "tests/test_application_logic.py::TestDataValidator::test_validate_patient_data_comprehensive", "tests/test_application_logic.py::TestDataValidator::test_validate_patient_data_missing_structure", "tests/test_application_logic.py::TestFormInputFunctions::test_mark_data_changed_functionality", "tests/test_application_logic.py::TestSessionStateManagement::test_initialize_session_state", "tests/test_application_logic.py::TestSessionStateManagement::test_mark_data_changed", "tests/test_application_logic.py::TestSmokeTests::test_basic_field_validation_smoke", "tests/test_application_logic.py::TestSmokeTests::test_data_validator_creation", "tests/test_application_logic.py::TestSmokeTests::test_export_data_preparation_smoke", "tests/test_application_logic.py::TestSmokeTests::test_patient_data_structure_smoke", "tests/test_application_logic.py::TestSmokeTests::test_session_state_initialization_smoke", "tests/test_application_logic.py::TestUtilityFunctions::test_test_utilities_assert_patient_data_equal", "tests/test_application_logic.py::TestUtilityFunctions::test_test_utilities_create_patient_data", "tests/test_database_unit.py::TestAutoSaveSystem::test_process_save_batch_connection_failure", "tests/test_database_unit.py::TestAutoSaveSystem::test_process_save_batch_partial_failure", "tests/test_database_unit.py::TestAutoSaveSystem::test_process_save_batch_success", "tests/test_database_unit.py::TestAutoSaveSystem::test_queue_auto_save_no_callback", "tests/test_database_unit.py::TestAutoSaveSystem::test_queue_auto_save_queue_full", "tests/test_database_unit.py::TestAutoSaveSystem::test_queue_auto_save_success", "tests/test_database_unit.py::TestCRUDOperations::test_get_patient_data_database_error", "tests/test_database_unit.py::TestCRUDOperations::test_get_patient_data_not_found", "tests/test_database_unit.py::TestCRUDOperations::test_get_patient_data_success", "tests/test_database_unit.py::TestCRUDOperations::test_insert_patient_data_connection_error", "tests/test_database_unit.py::TestCRUDOperations::test_insert_patient_data_success", "tests/test_database_unit.py::TestCRUDOperations::test_save_patient_data_invalid_format", "tests/test_database_unit.py::TestCRUDOperations::test_save_patient_data_no_changes", "tests/test_database_unit.py::TestCRUDOperations::test_save_patient_data_success", "tests/test_database_unit.py::TestCRUDOperations::test_save_patient_data_update_existing", "tests/test_database_unit.py::TestCRUDOperations::test_save_patient_data_validation_error", "tests/test_database_unit.py::TestDataValidation::test_calculate_data_checksum", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_empty_primary_diagnosis", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_invalid_age", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_invalid_gender", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_invalid_suicide_ideation", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_safety_plan_required", "tests/test_database_unit.py::TestDataValidation::test_validate_patient_data_success", "tests/test_database_unit.py::TestDatabaseConnection::test_database_initialization_failure", "tests/test_database_unit.py::TestDatabaseConnection::test_database_initialization_success", "tests/test_database_unit.py::TestDatabaseConnection::test_get_db_connection_closed_connection", "tests/test_database_unit.py::TestDatabaseConnection::test_get_db_connection_operational_error", "tests/test_database_unit.py::TestDatabaseConnection::test_get_db_connection_success", "tests/test_database_unit.py::TestDatabaseConnection::test_health_check_database_error", "tests/test_database_unit.py::TestDatabaseConnection::test_health_check_failure", "tests/test_database_unit.py::TestDatabaseConnection::test_health_check_success", "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_max_retries_exceeded", "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_non_retryable_error", "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_success_after_retries", "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_success_first_attempt", "tests/test_database_unit.py::TestTableCreation::test_create_performance_indexes", "tests/test_database_unit.py::TestTableCreation::test_create_tables_database_error", "tests/test_database_unit.py::TestTableCreation::test_create_tables_success", "tests/test_error_handling.py::TestValidationErrorHandling::test_invalid_data_types", "tests/test_error_handling.py::TestValidationErrorHandling::test_invalid_enum_values", "tests/test_error_handling.py::TestValidationErrorHandling::test_invalid_patient_data_format", "tests/test_error_handling.py::TestValidationErrorHandling::test_missing_critical_fields", "tests/test_error_handling.py::TestValidationErrorHandling::test_out_of_range_values", "tests/test_error_handling.py::TestValidationErrorHandling::test_safety_plan_validation_errors"]