"""
Comprehensive tests for application logic, data validation, and business rules.
Tests form processing, session state management, and validation logic.
"""

import pytest
import json
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timedelta
from typing import Dict, Any

# Import application modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock streamlit before importing the app
mock_st = MagicMock()
mock_st.session_state = MagicMock()
mock_st.session_state.load_time_threshold = 2.0
mock_st.session_state.performance_mode = True
mock_st.session_state.debug = False
sys.modules['streamlit'] = mock_st

# Import after mocking streamlit
from psychiatric_assessment_app import (
    DataValidator, initialize_session_state, prepare_export_data,
    enhanced_auto_save, mark_data_changed, check_connection_health
)


class TestDataValidator:
    """Test the DataValidator class and validation logic"""

    @pytest.fixture
    def validator(self):
        """Create DataValidator instance for testing"""
        return DataValidator()

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_valid_age(self, validator):
        """Test validation of valid age field"""
        field_config = {'type': int, 'min': 0, 'max': 120, 'critical': True}

        errors, warnings, status = validator.validate_field('demographics.age', 35, field_config)

        assert len(errors) == 0
        assert len(warnings) == 0
        assert status == 'complete'

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_invalid_age_negative(self, validator):
        """Test validation of negative age"""
        field_config = {'type': int, 'min': 0, 'max': 120, 'critical': True}

        errors, warnings, status = validator.validate_field('demographics.age', -5, field_config)

        assert len(errors) > 0
        assert "below minimum" in errors[0]
        assert status == 'error'

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_invalid_age_too_high(self, validator):
        """Test validation of age above maximum"""
        field_config = {'type': int, 'min': 0, 'max': 120, 'critical': True}

        errors, warnings, status = validator.validate_field('demographics.age', 150, field_config)

        assert len(warnings) > 0
        assert "above typical maximum" in warnings[0]
        assert status == 'complete'  # Warning, not error

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_empty_critical(self, validator):
        """Test validation of empty critical field"""
        field_config = {'type': str, 'critical': True}

        errors, warnings, status = validator.validate_field('chief_complaint.complaint', '', field_config)

        assert len(errors) > 0
        assert "Required critical field" in errors[0]
        assert status == 'empty'

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_empty_non_critical(self, validator):
        """Test validation of empty non-critical field"""
        field_config = {'type': str, 'critical': False}

        errors, warnings, status = validator.validate_field('optional.field', '', field_config)

        assert len(errors) == 0
        assert status == 'empty'

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_invalid_type(self, validator):
        """Test validation of invalid field type"""
        field_config = {'type': int, 'critical': False}

        errors, warnings, status = validator.validate_field('demographics.age', 'not_a_number', field_config)

        assert len(errors) > 0
        assert "Invalid type" in errors[0]
        assert status == 'error'

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_string_length_warning(self, validator):
        """Test validation of string length warnings"""
        field_config = {'type': str, 'min_length': 10, 'max_length': 100}

        # Test too short
        errors, warnings, status = validator.validate_field('field', 'short', field_config)
        assert len(warnings) > 0
        assert "too brief" in warnings[0]

        # Test too long
        long_text = 'x' * 150
        errors, warnings, status = validator.validate_field('field', long_text, field_config)
        assert len(warnings) > 0
        assert "too long" in warnings[0]

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_field_options_validation(self, validator):
        """Test validation against allowed options"""
        field_config = {'type': str, 'options': ['Male', 'Female', 'Non-binary', 'Other']}

        # Valid option
        errors, warnings, status = validator.validate_field('demographics.gender', 'Female', field_config)
        assert len(errors) == 0
        assert len(warnings) == 0

        # Invalid option
        errors, warnings, status = validator.validate_field('demographics.gender', 'InvalidGender', field_config)
        assert len(warnings) > 0
        assert "unexpected value" in warnings[0]

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_comprehensive(self, validator, sample_demographics_data, sample_risk_assessment_data):
        """Test comprehensive patient data validation"""
        patient_data = {
            'demographics': sample_demographics_data,
            'risk_assessment': sample_risk_assessment_data,
            'chief_complaint': {
                'complaint': 'Patient reports feeling depressed for several weeks',
                'symptom_duration': '3 weeks'
            }
        }

        errors, warnings, field_status = validator.validate_patient_data(patient_data)

        # Should have minimal errors with valid sample data
        assert len(errors) == 0
        assert isinstance(field_status, dict)
        assert len(field_status) > 0

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_missing_structure(self, validator):
        """Test validation with missing data structure"""
        patient_data = {}  # Empty data

        errors, warnings, field_status = validator.validate_patient_data(patient_data)

        # Should handle missing sections gracefully
        assert isinstance(errors, list)
        assert isinstance(warnings, list)
        assert isinstance(field_status, dict)


class TestSessionStateManagement:
    """Test session state initialization and management"""

    @pytest.mark.unit
    def test_initialize_session_state(self, mock_streamlit_session):
        """Test session state initialization"""
        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            initialize_session_state()

            # Check that default values are set
            assert hasattr(mock_streamlit_session, 'patient_data')
            assert hasattr(mock_streamlit_session, 'current_section')
            assert hasattr(mock_streamlit_session, 'patient_id')
            assert hasattr(mock_streamlit_session, 'debug')
            assert hasattr(mock_streamlit_session, 'auto_save_enabled')

    @pytest.mark.unit
    def test_mark_data_changed(self, mock_streamlit_session):
        """Test data change marking functionality"""
        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            with patch('psychiatric_assessment_app.validator') as mock_validator:
                mock_validator.validate_patient_data.return_value = ([], [], {})

                mark_data_changed()

                assert mock_streamlit_session.data_changed is True
                assert hasattr(mock_streamlit_session, 'last_validation')
                mock_validator.validate_patient_data.assert_called_once()


class TestDataExportAndPreparation:
    """Test data export and preparation functionality"""

    @pytest.mark.unit
    def test_prepare_export_data_basic(self, mock_streamlit_session):
        """Test basic export data preparation"""
        # Setup mock session state
        mock_streamlit_session.patient_id = "TEST-12345678"
        mock_streamlit_session.assessment_start_time = datetime.now()
        mock_streamlit_session.patient_data = {"demographics": {"age": 35}}
        mock_streamlit_session.field_completion_status = {"demographics.age": "complete"}
        mock_streamlit_session.validation_errors = []
        mock_streamlit_session.validation_warnings = []
        mock_streamlit_session.last_validation = datetime.now()
        mock_streamlit_session.data_validation_level = "standard"
        mock_streamlit_session.required_fields_met = 5
        mock_streamlit_session.total_required_fields = 25
        mock_streamlit_session.assessment_flow_mode = "comprehensive"
        mock_streamlit_session.render_count = 10
        mock_streamlit_session.last_render_time = 0.5
        mock_streamlit_session.load_time_threshold = 2.0

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            export_data = prepare_export_data()

            # Verify export data structure
            assert 'metadata' in export_data
            assert 'clinical_data' in export_data
            assert 'field_completion' in export_data
            assert 'validation_results' in export_data

            # Verify metadata content
            metadata = export_data['metadata']
            assert metadata['patient_id'] == "TEST-12345678"
            assert 'assessment_date' in metadata
            assert 'duration_minutes' in metadata
            assert metadata['data_version'] == '2.0'

            # Verify clinical data
            assert export_data['clinical_data'] == {"demographics": {"age": 35}}

            # Verify field completion
            assert export_data['field_completion'] == {"demographics.age": "complete"}

    @pytest.mark.unit
    def test_prepare_export_data_completion_percentage(self, mock_streamlit_session):
        """Test completion percentage calculation in export data"""
        mock_streamlit_session.patient_id = "TEST-12345678"
        mock_streamlit_session.assessment_start_time = datetime.now()
        mock_streamlit_session.patient_data = {
            "demographics": {"age": 35},
            "chief_complaint": {"complaint": "Test"},
            "risk_assessment": {"current_si": "None"}
        }

        # Mock other required attributes
        for attr in ['field_completion_status', 'validation_errors', 'validation_warnings',
                    'last_validation', 'data_validation_level', 'required_fields_met',
                    'total_required_fields', 'assessment_flow_mode', 'render_count',
                    'last_render_time', 'load_time_threshold']:
            if not hasattr(mock_streamlit_session, attr):
                setattr(mock_streamlit_session, attr, None)

        mock_streamlit_session.field_completion_status = {}
        mock_streamlit_session.validation_errors = []
        mock_streamlit_session.validation_warnings = []
        mock_streamlit_session.last_validation = datetime.now()
        mock_streamlit_session.data_validation_level = "standard"
        mock_streamlit_session.required_fields_met = 3
        mock_streamlit_session.total_required_fields = 25
        mock_streamlit_session.assessment_flow_mode = "comprehensive"
        mock_streamlit_session.render_count = 5
        mock_streamlit_session.last_render_time = 0.3
        mock_streamlit_session.load_time_threshold = 2.0

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            export_data = prepare_export_data()

            # Completion percentage should be calculated based on filled sections
            completion_pct = export_data['metadata']['completion_percentage']
            expected_pct = 3 / 16 * 100  # 3 sections out of 16 total
            assert abs(completion_pct - expected_pct) < 1.0  # Allow small rounding differences


@pytest.mark.smoke
class TestSmokeTests:
    """Quick smoke tests for basic functionality"""

    @pytest.mark.unit
    def test_data_validator_creation(self):
        """Smoke test: DataValidator can be created"""
        validator = DataValidator()
        assert validator is not None
        assert hasattr(validator, 'required_fields')
        assert hasattr(validator, 'validate_field')
        assert hasattr(validator, 'validate_patient_data')

    @pytest.mark.unit
    def test_basic_field_validation_smoke(self):
        """Smoke test: Basic field validation works"""
        validator = DataValidator()

        # Test valid age
        errors, warnings, status = validator.validate_field(
            'demographics.age', 25, {'type': int, 'min': 0, 'max': 120}
        )
        assert status in ['complete', 'partial', 'empty', 'error']
        assert isinstance(errors, list)
        assert isinstance(warnings, list)

    @pytest.mark.unit
    def test_patient_data_structure_smoke(self, sample_patient_data):
        """Smoke test: Sample patient data has expected structure"""
        assert isinstance(sample_patient_data, dict)
        assert 'demographics' in sample_patient_data
        assert 'chief_complaint' in sample_patient_data
        assert 'risk_assessment' in sample_patient_data

    @pytest.mark.unit
    def test_export_data_preparation_smoke(self, mock_streamlit_session):
        """Smoke test: Export data preparation doesn't crash"""
        # Setup minimal session state
        mock_streamlit_session.patient_id = "SMOKE-TEST-001"
        mock_streamlit_session.assessment_start_time = datetime.now()
        mock_streamlit_session.patient_data = {}

        # Set all required attributes to avoid AttributeError
        required_attrs = [
            'field_completion_status', 'validation_errors', 'validation_warnings',
            'last_validation', 'data_validation_level', 'required_fields_met',
            'total_required_fields', 'assessment_flow_mode', 'render_count',
            'last_render_time', 'load_time_threshold'
        ]

        for attr in required_attrs:
            setattr(mock_streamlit_session, attr, None)

        # Set specific values for required fields
        mock_streamlit_session.field_completion_status = {}
        mock_streamlit_session.validation_errors = []
        mock_streamlit_session.validation_warnings = []
        mock_streamlit_session.last_validation = datetime.now()
        mock_streamlit_session.data_validation_level = "standard"
        mock_streamlit_session.required_fields_met = 0
        mock_streamlit_session.total_required_fields = 25
        mock_streamlit_session.assessment_flow_mode = "comprehensive"
        mock_streamlit_session.render_count = 1
        mock_streamlit_session.last_render_time = 0.1
        mock_streamlit_session.load_time_threshold = 2.0

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            export_data = prepare_export_data()

            # Basic structure check
            assert isinstance(export_data, dict)
            assert 'metadata' in export_data
            assert 'clinical_data' in export_data

    @pytest.mark.unit
    def test_session_state_initialization_smoke(self, mock_streamlit_session):
        """Smoke test: Session state initialization doesn't crash"""
        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            # Should not raise any exceptions
            initialize_session_state()

            # Check that some basic attributes were set
            assert hasattr(mock_streamlit_session, 'patient_data')
            assert hasattr(mock_streamlit_session, 'current_section')


class TestConnectionHealthChecking:
    """Test connection health checking functionality"""

    @pytest.mark.unit
    def test_check_connection_health_with_db(self, mock_streamlit_session):
        """Test connection health check when database is available"""
        # Mock database instance
        mock_db = MagicMock()
        mock_db.health_check.return_value = (True, "Database healthy")

        mock_streamlit_session.offline_mode = False
        mock_streamlit_session.last_connection_check = datetime.now() - timedelta(minutes=1)

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            with patch('psychiatric_assessment_app.db', mock_db):
                result = check_connection_health()

                assert isinstance(result, bool)

    @pytest.mark.unit
    def test_check_connection_health_no_db(self, mock_streamlit_session):
        """Test connection health check when database is not available"""
        mock_streamlit_session.offline_mode = True
        mock_streamlit_session.last_connection_check = datetime.now() - timedelta(minutes=1)

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            with patch('psychiatric_assessment_app.db', None):
                result = check_connection_health()

                assert isinstance(result, bool)


class TestAutoSaveFunctionality:
    """Test auto-save functionality"""

    @pytest.mark.unit
    def test_enhanced_auto_save_offline_mode(self, mock_streamlit_session):
        """Test auto-save when in offline mode"""
        mock_streamlit_session.offline_mode = True
        mock_streamlit_session.save_in_progress = False
        mock_streamlit_session.data_changed = True

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            with patch('psychiatric_assessment_app.db', None):
                result = enhanced_auto_save()

                assert result is False

    @pytest.mark.unit
    def test_enhanced_auto_save_no_changes(self, mock_streamlit_session):
        """Test auto-save when no data changes"""
        mock_streamlit_session.offline_mode = False
        mock_streamlit_session.save_in_progress = False
        mock_streamlit_session.data_changed = False

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            result = enhanced_auto_save()

            assert result is False

    @pytest.mark.unit
    def test_enhanced_auto_save_already_saving(self, mock_streamlit_session):
        """Test auto-save when save is already in progress"""
        mock_streamlit_session.offline_mode = False
        mock_streamlit_session.save_in_progress = True
        mock_streamlit_session.data_changed = True

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            result = enhanced_auto_save()

            assert result is False


class TestFormInputFunctions:
    """Test form input helper functions (if accessible)"""

    @pytest.mark.unit
    def test_mark_data_changed_functionality(self, mock_streamlit_session):
        """Test mark_data_changed function"""
        mock_streamlit_session.data_changed = False
        mock_streamlit_session.data_validation_level = "standard"
        mock_streamlit_session.patient_data = {}

        with patch('psychiatric_assessment_app.st.session_state', mock_streamlit_session):
            with patch('psychiatric_assessment_app.validator') as mock_validator:
                mock_validator.validate_patient_data.return_value = ([], [], {})

                mark_data_changed()

                assert mock_streamlit_session.data_changed is True
                assert hasattr(mock_streamlit_session, 'last_validation')
                mock_validator.validate_patient_data.assert_called_once()


class TestUtilityFunctions:
    """Test utility functions and helpers"""

    @pytest.mark.unit
    def test_test_utilities_create_patient_data(self, test_utilities):
        """Test utility function for creating test patient data"""
        data = test_utilities.create_test_patient_data(
            scenario="major_depression",
            **{"demographics.age": 40}
        )

        assert isinstance(data, dict)
        assert 'demographics' in data
        assert data['demographics']['age'] == 40

    @pytest.mark.unit
    def test_test_utilities_assert_patient_data_equal(self, test_utilities):
        """Test utility function for comparing patient data"""
        data1 = {
            "demographics": {"age": 35, "gender": "Female"},
            "assessment_date": "2023-01-01"
        }

        data2 = {
            "demographics": {"age": 35, "gender": "Female"},
            "assessment_date": "2023-01-02"  # Different date
        }

        # Should not raise exception when ignoring assessment_date
        test_utilities.assert_patient_data_equal(
            data1, data2, ignore_keys=["assessment_date"]
        )

        # Should raise exception when not ignoring assessment_date
        with pytest.raises(AssertionError):
            test_utilities.assert_patient_data_equal(data1, data2, ignore_keys=[])