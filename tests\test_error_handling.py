"""
Comprehensive error handling tests for the psychiatric assessment application.
Tests database connection failures, validation errors, and recovery mechanisms.
"""

import pytest
import time
from unittest.mock import Mo<PERSON>, MagicMock, patch
from datetime import datetime
import psycopg2

from database import PsychiatricAssessmentDB, DatabaseError, ValidationError


@pytest.mark.error_handling
class TestDatabaseErrorHandling:
    """Test database error scenarios and recovery mechanisms"""

    @pytest.mark.unit
    def test_connection_timeout_handling(self, mock_psychiatric_db):
        """Test handling of database connection timeouts"""
        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = psycopg2.OperationalError("connection timeout")

            with pytest.raises(DatabaseError, match="Database connection failed"):
                with mock_psychiatric_db.get_db_connection():
                    pass

    @pytest.mark.unit
    def test_connection_refused_handling(self, mock_psychiatric_db):
        """Test handling of connection refused errors"""
        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = psycopg2.OperationalError("connection refused")

            with pytest.raises(DatabaseError):
                with mock_psychiatric_db.get_db_connection():
                    pass

    @pytest.mark.unit
    def test_authentication_failure_handling(self, mock_psychiatric_db):
        """Test handling of authentication failures"""
        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = psycopg2.OperationalError("authentication failed")

            with pytest.raises(DatabaseError):
                with mock_psychiatric_db.get_db_connection():
                    pass

    @pytest.mark.unit
    def test_database_not_found_handling(self, mock_psychiatric_db):
        """Test handling when database doesn't exist"""
        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = psycopg2.OperationalError("database does not exist")

            with pytest.raises(DatabaseError):
                with mock_psychiatric_db.get_db_connection():
                    pass

    @pytest.mark.unit
    def test_table_not_found_handling(self, mock_psychiatric_db, mock_db_connection):
        """Test handling when required tables don't exist"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.ProgrammingError("relation does not exist")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with pytest.raises(Exception):  # Should propagate the error
                mock_psychiatric_db.get_patient_data("TEST-12345678")

    @pytest.mark.unit
    def test_constraint_violation_handling(self, mock_psychiatric_db, mock_db_connection):
        """Test handling of database constraint violations"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.IntegrityError("constraint violation")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            patient_data = {
                "metadata": {"patient_id": "INVALID-FORMAT"},
                "clinical_data": {}
            }

            with pytest.raises(Exception):
                mock_psychiatric_db.insert_patient_data(patient_data)

    @pytest.mark.unit
    def test_data_too_long_handling(self, mock_psychiatric_db, mock_db_connection):
        """Test handling of data too long errors"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.DataError("value too long")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            patient_data = {
                "metadata": {"patient_id": "TEST-12345678"},
                "clinical_data": {"demographics": {"occupation": "x" * 1000}}  # Too long
            }

            with pytest.raises(Exception):
                mock_psychiatric_db.insert_patient_data(patient_data)

    @pytest.mark.unit
    def test_deadlock_detection_handling(self, mock_psychiatric_db, mock_db_connection):
        """Test handling of database deadlocks"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.OperationalError("deadlock detected")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with pytest.raises(Exception):
                mock_psychiatric_db.get_patient_data("TEST-12345678")

    @pytest.mark.unit
    def test_disk_full_handling(self, mock_psychiatric_db, mock_db_connection):
        """Test handling of disk full errors"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.OperationalError("disk full")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            patient_data = {
                "metadata": {"patient_id": "TEST-12345678"},
                "clinical_data": {}
            }

            with pytest.raises(Exception):
                mock_psychiatric_db.insert_patient_data(patient_data)


@pytest.mark.error_handling
class TestValidationErrorHandling:
    """Test validation error scenarios and handling"""

    @pytest.mark.unit
    @pytest.mark.validation
    def test_invalid_patient_data_format(self, mock_psychiatric_db):
        """Test handling of invalid patient data format"""
        invalid_formats = [
            "not_a_dict",
            123,
            None,
            [],
            {"invalid": "structure"}
        ]

        for invalid_data in invalid_formats:
            with pytest.raises((ValidationError, TypeError, AttributeError)):
                mock_psychiatric_db._validate_patient_data(invalid_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_missing_critical_fields(self, mock_psychiatric_db):
        """Test handling of missing critical fields"""
        incomplete_data = {
            "demographics": {},  # Missing age and gender
            "risk_assessment": {}  # Missing suicide ideation
        }

        # Should not raise exception for missing fields (they're handled as empty)
        mock_psychiatric_db._validate_patient_data(incomplete_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_invalid_data_types(self, mock_psychiatric_db):
        """Test handling of invalid data types"""
        invalid_data = {
            "demographics": {
                "age": "not_a_number",  # Should be int
                "gender": 123,  # Should be string
                "emergency_contact": "not_a_dict"  # Should be dict
            }
        }

        with pytest.raises(ValidationError):
            mock_psychiatric_db._validate_patient_data(invalid_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_out_of_range_values(self, mock_psychiatric_db):
        """Test handling of out-of-range values"""
        out_of_range_data = {
            "demographics": {
                "age": -10  # Negative age
            },
            "risk_assessment": {
                "si_intensity": 15  # Out of 1-10 range
            }
        }

        with pytest.raises(ValidationError):
            mock_psychiatric_db._validate_patient_data(out_of_range_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_invalid_enum_values(self, mock_psychiatric_db):
        """Test handling of invalid enumeration values"""
        invalid_enum_data = {
            "demographics": {
                "gender": "InvalidGender"
            },
            "risk_assessment": {
                "current_si": "InvalidSuicideLevel"
            }
        }

        with pytest.raises(ValidationError):
            mock_psychiatric_db._validate_patient_data(invalid_enum_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_safety_plan_validation_errors(self, mock_psychiatric_db):
        """Test safety plan validation error scenarios"""
        # High-risk patient without safety plan
        high_risk_no_plan = {
            "risk_assessment": {
                "current_si": "Active with plan",
                "safety_plan_created": False
            }
        }

        with pytest.raises(ValidationError, match="Safety plan required"):
            mock_psychiatric_db._validate_patient_data(high_risk_no_plan, "TEST-12345678")

        # Imminent risk without safety plan
        imminent_risk_no_plan = {
            "risk_assessment": {
                "current_si": "Imminent risk",
                "safety_plan_created": False
            }
        }

        with pytest.raises(ValidationError, match="Safety plan required"):
            mock_psychiatric_db._validate_patient_data(imminent_risk_no_plan, "TEST-12345678")


@pytest.mark.error_handling
class TestRecoveryMechanisms:
    """Test error recovery and resilience mechanisms"""

    @pytest.mark.unit
    def test_retry_mechanism_success_after_failure(self, mock_psychiatric_db):
        """Test retry mechanism succeeding after initial failures"""
        call_count = 0

        def failing_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise psycopg2.OperationalError("Temporary failure")
            return "success"

        # Apply retry decorator
        retried_operation = mock_psychiatric_db.retry_on_failure(max_retries=3, delay=0.1)(failing_operation)

        result = retried_operation()
        assert result == "success"
        assert call_count == 3

    @pytest.mark.unit
    def test_retry_mechanism_max_retries_exceeded(self, mock_psychiatric_db):
        """Test retry mechanism when max retries are exceeded"""
        def always_failing_operation():
            raise psycopg2.OperationalError("Persistent failure")

        retried_operation = mock_psychiatric_db.retry_on_failure(max_retries=2, delay=0.1)(always_failing_operation)

        with pytest.raises(DatabaseError, match="Database operation failed after 2 retries"):
            retried_operation()

    @pytest.mark.unit
    def test_connection_pool_recovery(self, mock_psychiatric_db, mock_db_connection):
        """Test connection pool recovery after failures"""
        mock_conn, mock_cursor = mock_db_connection

        # Simulate connection failure followed by recovery
        mock_psychiatric_db.pool.getconn.side_effect = [
            psycopg2.OperationalError("Connection failed"),
            mock_conn  # Recovery
        ]

        # First attempt should fail
        with pytest.raises(DatabaseError):
            with mock_psychiatric_db.get_db_connection():
                pass

        # Second attempt should succeed (if retry logic is implemented)
        # This would depend on the actual implementation

    @pytest.mark.unit
    def test_graceful_degradation_offline_mode(self, mock_psychiatric_db):
        """Test graceful degradation to offline mode"""
        # Simulate database unavailability
        mock_psychiatric_db.pool = None

        # Health check should indicate offline status
        healthy, message = mock_psychiatric_db.health_check()
        assert healthy is False
        assert "Connection pool not initialized" in message

    @pytest.mark.unit
    def test_auto_save_queue_overflow_handling(self, mock_psychiatric_db, sample_patient_data):
        """Test handling of auto-save queue overflow"""
        # Fill the queue to capacity
        for i in range(50):  # Queue limit is 50
            mock_psychiatric_db.queue_auto_save(sample_patient_data, None, priority=1)

        # Next save should fail gracefully
        callback = MagicMock()
        result = mock_psychiatric_db.queue_auto_save(sample_patient_data, callback, priority=1)

        assert result is False
        callback.assert_called_once_with(False, None, "Queue full")

    @pytest.mark.unit
    def test_transaction_rollback_on_error(self, mock_psychiatric_db, mock_db_connection):
        """Test transaction rollback on errors"""
        mock_conn, mock_cursor = mock_db_connection

        # Simulate error during transaction
        mock_cursor.execute.side_effect = [None, psycopg2.IntegrityError("Constraint violation")]

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            patient_data = {
                "metadata": {"patient_id": "TEST-12345678"},
                "clinical_data": {}
            }

            with pytest.raises(Exception):
                mock_psychiatric_db.insert_patient_data(patient_data)

            # Verify rollback was called
            mock_conn.rollback.assert_called()


@pytest.mark.error_handling
class TestEdgeCases:
    """Test edge cases and boundary conditions"""

    @pytest.mark.unit
    def test_empty_patient_data(self, mock_psychiatric_db):
        """Test handling of completely empty patient data"""
        empty_data = {
            "metadata": {"patient_id": "TEST-EMPTY-001"},
            "clinical_data": {}
        }

        # Should not raise exception, but may have validation warnings
        mock_psychiatric_db._validate_patient_data(empty_data["clinical_data"], "TEST-EMPTY-001")

    @pytest.mark.unit
    def test_extremely_large_data(self, mock_psychiatric_db):
        """Test handling of extremely large data sets"""
        large_text = "x" * 10000  # Very large text field

        large_data = {
            "demographics": {
                "occupation": large_text
            },
            "chief_complaint": {
                "complaint": large_text
            }
        }

        # Should handle large data gracefully (may warn about length)
        mock_psychiatric_db._validate_patient_data(large_data, "TEST-LARGE-001")

    @pytest.mark.unit
    def test_unicode_and_special_characters(self, mock_psychiatric_db):
        """Test handling of Unicode and special characters"""
        unicode_data = {
            "demographics": {
                "occupation": "Médecin spécialisé en psychiatrie 🧠",
                "primary_language": "Français"
            },
            "chief_complaint": {
                "complaint": "Patient reports feeling 'très triste' with émotions difficiles"
            }
        }

        # Should handle Unicode characters without issues
        mock_psychiatric_db._validate_patient_data(unicode_data, "TEST-UNICODE-001")

    @pytest.mark.unit
    def test_null_and_none_values(self, mock_psychiatric_db):
        """Test handling of null and None values"""
        null_data = {
            "demographics": {
                "age": None,
                "gender": None,
                "occupation": ""
            },
            "risk_assessment": {
                "current_si": None,
                "safety_plan_created": None
            }
        }

        # Should handle None values gracefully
        mock_psychiatric_db._validate_patient_data(null_data, "TEST-NULL-001")

    @pytest.mark.unit
    def test_concurrent_modification_detection(self, mock_psychiatric_db, mock_db_connection):
        """Test detection of concurrent modifications"""
        mock_conn, mock_cursor = mock_db_connection

        # Simulate existing record with different checksum (indicating concurrent modification)
        mock_cursor.fetchone.return_value = {
            'data_checksum': 'different_checksum',
            'updated_at': datetime.now()
        }

        patient_data = {
            "metadata": {"patient_id": "TEST-CONCURRENT-001"},
            "clinical_data": {"demographics": {"age": 35}}
        }

        # Should proceed with update despite different checksum
        result = mock_psychiatric_db._save_patient_data_sync(mock_cursor, patient_data)
        assert result == "TEST-CONCURRENT-001"