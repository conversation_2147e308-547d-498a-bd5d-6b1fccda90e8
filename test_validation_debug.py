#!/usr/bin/env python3
"""
Debug script to test validation logic
"""

from database import PsychiatricAssessmentDB, ValidationError

def test_validation():
    """Test the validation logic directly"""
    
    # Create database instance without initialization
    db = PsychiatricAssessmentDB(skip_init=True)
    
    # Test cases
    test_cases = [
        ("string", "not_a_dict"),
        ("number", 123),
        ("none", None),
        ("list", []),
        ("invalid_dict", {"invalid": "structure"})
    ]
    
    for test_name, test_data in test_cases:
        try:
            print(f"Testing {test_name}: {test_data}")
            db._validate_patient_data(test_data, "TEST-123")
            print(f"  ❌ No exception raised for {test_name}")
        except ValidationError as e:
            print(f"  ✅ ValidationError raised for {test_name}: {e}")
        except Exception as e:
            print(f"  ✅ Other exception raised for {test_name}: {type(e).__name__}: {e}")
        print()

if __name__ == "__main__":
    test_validation()
