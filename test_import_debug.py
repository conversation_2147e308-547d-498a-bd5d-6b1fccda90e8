#!/usr/bin/env python3
"""
Debug script to test imports and identify issues
"""

import sys
import traceback

def test_basic_imports():
    """Test basic Python imports"""
    try:
        import os
        print("✓ os imported successfully")
        
        import json
        print("✓ json imported successfully")
        
        import uuid
        print("✓ uuid imported successfully")
        
        import datetime
        print("✓ datetime imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        traceback.print_exc()
        return False

def test_third_party_imports():
    """Test third-party imports"""
    try:
        import psycopg2
        print("✓ psycopg2 imported successfully")
        
        import pytest
        print("✓ pytest imported successfully")
        
        import pandas
        print("✓ pandas imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Third-party imports failed: {e}")
        traceback.print_exc()
        return False

def test_local_imports():
    """Test local module imports"""
    try:
        # Add current directory to path
        import os
        sys.path.insert(0, os.getcwd())
        
        import test_data_generator
        print("✓ test_data_generator imported successfully")
        
        # Try importing database module step by step
        print("Attempting to import database module...")
        import database
        print("✓ database imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Local imports failed: {e}")
        traceback.print_exc()
        return False

def test_database_class():
    """Test database class instantiation"""
    try:
        from database import PsychiatricAssessmentDB, DatabaseError, ValidationError
        print("✓ Database classes imported successfully")
        
        # Try to create instance without connecting
        print("Testing database class instantiation...")
        # This might hang if there's a connection attempt in __init__
        
        return True
    except Exception as e:
        print(f"❌ Database class test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Import Debug Test ===")
    
    print("\n1. Testing basic imports...")
    if not test_basic_imports():
        sys.exit(1)
    
    print("\n2. Testing third-party imports...")
    if not test_third_party_imports():
        sys.exit(1)
    
    print("\n3. Testing local imports...")
    if not test_local_imports():
        sys.exit(1)
    
    print("\n4. Testing database class...")
    if not test_database_class():
        sys.exit(1)
    
    print("\n✅ All imports successful!")
