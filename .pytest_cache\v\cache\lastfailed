{"tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_success_first_attempt": true, "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_success_after_retries": true, "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_max_retries_exceeded": true, "tests/test_database_unit.py::TestRetryMechanism::test_retry_decorator_non_retryable_error": true, "tests/test_application_logic.py::TestUtilityFunctions::test_test_utilities_assert_patient_data_equal": true}