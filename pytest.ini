[tool:pytest]
# Pytest configuration for psychiatric assessment application testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output and reporting
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=85
    --durations=10
    --maxfail=5

# Markers for test categorization
markers =
    unit: Unit tests for individual functions/methods
    integration: Integration tests for component interactions
    database: Tests requiring database connection
    slow: Tests that take longer than 1 second
    performance: Performance and load tests
    error_handling: Tests for error scenarios and edge cases
    validation: Tests for data validation logic
    security: Security-related tests
    smoke: Quick smoke tests for basic functionality
    regression: Regression tests for bug fixes

# Test environment
env =
    TESTING = true
    DB_HOST = localhost
    DB_NAME = psychiatric_test_db
    DB_USER = postgres
    DB_PASSWORD = test_password
    DB_PORT = 5432

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:streamlit.*

# Minimum version requirements
minversion = 7.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment for parallel execution with pytest-xdist