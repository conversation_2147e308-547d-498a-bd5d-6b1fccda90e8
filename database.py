import psycopg2
from psycopg2.extras import RealDictCursor, execute_batch
from psycopg2 import pool, sql
import json
import os
import uuid
import logging
from datetime import datetime, timedelta
import time
import threading
from contextlib import contextmanager
import queue
import hashlib
import pickle
import asyncio
from typing import Dict, Any, List, Optional, Tuple
import traceback
from functools import wraps

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('psychiatric_db.log', mode='a', encoding='utf-8')
    ]
)

class DatabaseError(Exception):
    """Custom database exception"""
    pass

class ValidationError(Exception):
    """Custom validation exception"""
    pass

def retry_on_failure(max_retries=3, delay=1, backoff=2):
    """Decorator for retrying database operations on failure"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
                    retries += 1
                    if retries >= max_retries:
                        logging.error(f"❌ Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise DatabaseError(f"Database operation failed after {max_retries} retries: {str(e)}")
                    
                    wait_time = delay * (backoff ** (retries - 1))
                    logging.warning(f"⚠️ Retry {retries}/{max_retries} for {func.__name__} in {wait_time}s: {e}")
                    time.sleep(wait_time)
                except Exception as e:
                    logging.error(f"❌ Non-retryable error in {func.__name__}: {e}")
                    raise
            return None
        return wrapper
    return decorator

class PsychiatricAssessmentDB:
    def __init__(self, skip_init=False):
        """Initialize enhanced PostgreSQL connection with monitoring"""
        self.db_params = {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'database': os.environ.get('DB_NAME', 'psychiatric_assessments'),
            'user': os.environ.get('DB_USER', 'postgres'),
            'password': os.environ.get('DB_PASSWORD', 'password'),
            'port': int(os.environ.get('DB_PORT', 5432)),
            'application_name': 'psychiatric_assessment_app',
            'connect_timeout': 30,
            'options': '-c statement_timeout=30s'
        }

        # Enhanced configuration
        self.pool = None
        self.connection_timeout = 30
        self.retry_attempts = 3
        self.retry_delay = 1
        self.max_connections = 20
        self.min_connections = 2

        # Performance monitoring
        self.query_performance_log = []
        self.connection_metrics = {
            'total_connections': 0,
            'failed_connections': 0,
            'avg_query_time': 0,
            'slow_queries': 0
        }

        # Auto-save system
        self.auto_save_queue = queue.PriorityQueue()
        self.auto_save_thread = None
        self.auto_save_running = False
        self.save_batch_size = 10
        self.save_batch_timeout = 5  # seconds

        # Data integrity
        self.data_checksums = {}
        self.backup_retention_days = 30

        # Cache system
        self.query_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.cache_max_size = 1000

        # Skip initialization for testing
        if not skip_init:
            self._init_pool()
            self._start_auto_save_worker()
            self._start_maintenance_worker()
    
    def _init_pool(self):
        """Initialize connection pool with enhanced error handling"""
        for attempt in range(self.retry_attempts):
            try:
                self.pool = psycopg2.pool.ThreadedConnectionPool(
                    minconn=self.min_connections,
                    maxconn=self.max_connections,
                    cursor_factory=RealDictCursor,
                    **self.db_params
                )
                
                # Test initial connection
                with self.get_db_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                
                logging.info(f"✅ PostgreSQL connection pool initialized (min={self.min_connections}, max={self.max_connections})")
                self.connection_metrics['total_connections'] += 1
                return
                
            except Exception as e:
                self.connection_metrics['failed_connections'] += 1
                logging.warning(f"⚠️ DB connection attempt {attempt + 1}/{self.retry_attempts} failed: {e}")
                
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    time.sleep(wait_time)
                else:
                    logging.error(f"❌ Failed to initialize DB pool after {self.retry_attempts} attempts")
                    logging.info("💡 Running in offline mode. Check PostgreSQL service and configuration.")
                    self.pool = None
    
    @contextmanager
    def get_db_connection(self):
        """Enhanced connection context manager with monitoring"""
        conn = None
        start_time = time.time()
        
        try:
            if not self.pool:
                raise DatabaseError("Database pool not initialized")
            
            conn = self.pool.getconn()
            
            if conn.closed:
                logging.warning("⚠️ Connection was closed, getting new one")
                self.pool.putconn(conn, close=True)
                conn = self.pool.getconn()
            
            # Set connection-level settings
            with conn.cursor() as cursor:
                cursor.execute("SET statement_timeout = '30s'")
                cursor.execute("SET lock_timeout = '10s'")
            
            yield conn
            
        except psycopg2.OperationalError as e:
            if conn:
                conn.rollback()
            logging.error(f"❌ Database operational error: {e}")
            raise DatabaseError(f"Database connection failed: {str(e)}")
            
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            logging.error(f"❌ Database operation failed: {e}")
            raise
            
        finally:
            if conn:
                try:
                    conn.commit()
                    connection_time = time.time() - start_time
                    
                    # Log slow connections
                    if connection_time > 5.0:
                        logging.warning(f"⚠️ Slow database operation: {connection_time:.2f}s")
                        self.connection_metrics['slow_queries'] += 1
                    
                    # Update metrics
                    total_time = self.connection_metrics['avg_query_time'] * self.connection_metrics['total_connections']
                    self.connection_metrics['total_connections'] += 1
                    self.connection_metrics['avg_query_time'] = (total_time + connection_time) / self.connection_metrics['total_connections']
                    
                except Exception as e:
                    logging.error(f"❌ Error during connection cleanup: {e}")
                finally:
                    self.pool.putconn(conn)
    
    @retry_on_failure()
    def health_check(self) -> Tuple[bool, str]:
        """Enhanced health check with detailed diagnostics"""
        try:
            if not self.pool:
                return False, "Connection pool not initialized"
            
            start_time = time.time()
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Basic connectivity test
                    cursor.execute("SELECT 1 as test")
                    result = cursor.fetchone()
                    
                    if not result or result['test'] != 1:
                        return False, "Basic query test failed"
                    
                    # Check database size and stats
                    cursor.execute("""
                        SELECT 
                            pg_size_pretty(pg_database_size(current_database())) as db_size,
                            (SELECT count(*) FROM patients) as patient_count,
                            version() as db_version
                    """)
                    
                    stats = cursor.fetchone()
                    response_time = time.time() - start_time
                    
                    health_info = {
                        'status': 'healthy',
                        'response_time_ms': round(response_time * 1000, 2),
                        'database_size': stats['db_size'],
                        'patient_count': stats['patient_count'],
                        'version': stats['db_version'].split()[0:2],
                        'pool_stats': {
                            'total_connections': self.connection_metrics['total_connections'],
                            'failed_connections': self.connection_metrics['failed_connections'],
                            'avg_query_time': round(self.connection_metrics['avg_query_time'], 3)
                        }
                    }
                    
                    return True, json.dumps(health_info)
                    
        except Exception as e:
            return False, f"Health check failed: {str(e)}"
    
    def _start_auto_save_worker(self):
        """Start enhanced auto-save worker with batching"""
        if not self.auto_save_running:
            self.auto_save_running = True
            self.auto_save_thread = threading.Thread(
                target=self._auto_save_worker, 
                daemon=True, 
                name="AutoSaveWorker"
            )
            self.auto_save_thread.start()
            logging.info("✅ Enhanced auto-save worker started")
    
    def _auto_save_worker(self):
        """Enhanced auto-save worker with batching and prioritization"""
        batch = []
        batch_start_time = time.time()
        
        while self.auto_save_running:
            try:
                try:
                    # Get save request with timeout
                    priority, save_request = self.auto_save_queue.get(timeout=1.0)
                except queue.Empty:
                    # Process any pending batch
                    if batch:
                        self._process_save_batch(batch)
                        batch = []
                        batch_start_time = time.time()
                    continue
                
                if save_request is None:  # Shutdown signal
                    break
                
                batch.append(save_request)
                
                # Process batch when full or timeout reached
                batch_age = time.time() - batch_start_time
                if len(batch) >= self.save_batch_size or batch_age >= self.save_batch_timeout:
                    self._process_save_batch(batch)
                    batch = []
                    batch_start_time = time.time()
                
                self.auto_save_queue.task_done()
                
            except Exception as e:
                logging.error(f"❌ Auto-save worker error: {e}")
                # Clear current batch on error
                batch = []
                batch_start_time = time.time()
        
        # Process any remaining batch on shutdown
        if batch:
            self._process_save_batch(batch)
    
    def _process_save_batch(self, batch):
        """Process a batch of save requests efficiently"""
        if not batch:
            return
        
        success_count = 0
        start_time = time.time()
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    for save_request in batch:
                        try:
                            patient_data, callback = save_request
                            patient_id = self._save_patient_data_sync(cursor, patient_data)
                            
                            if callback:
                                callback(True, patient_id, None)
                            success_count += 1
                            
                        except Exception as e:
                            logging.error(f"❌ Batch save item failed: {e}")
                            if len(save_request) > 1 and save_request[1]:  # Has callback
                                save_request[1](False, None, str(e))
            
            processing_time = time.time() - start_time
            logging.info(f"✅ Processed batch: {success_count}/{len(batch)} saves in {processing_time:.2f}s")
            
        except Exception as e:
            logging.error(f"❌ Batch processing failed: {e}")
            # Notify all callbacks of failure
            for save_request in batch:
                if len(save_request) > 1 and save_request[1]:
                    save_request[1](False, None, str(e))
    
    def _start_maintenance_worker(self):
        """Start maintenance worker for cleanup and optimization"""
        def maintenance_worker():
            while self.auto_save_running:
                try:
                    # Run maintenance every hour
                    time.sleep(3600)
                    
                    if not self.auto_save_running:
                        break
                    
                    self._run_maintenance_tasks()
                    
                except Exception as e:
                    logging.error(f"❌ Maintenance worker error: {e}")
        
        maintenance_thread = threading.Thread(
            target=maintenance_worker, 
            daemon=True, 
            name="MaintenanceWorker"
        )
        maintenance_thread.start()
        logging.info("✅ Maintenance worker started")
    
    def _run_maintenance_tasks(self):
        """Run regular maintenance tasks"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Clean old query cache entries
                    self._clean_query_cache()
                    
                    # Update table statistics
                    cursor.execute("ANALYZE")
                    
                    # Clean old backup files (if applicable)
                    self._clean_old_backups()
                    
                    # Log maintenance completion
                    logging.info("✅ Maintenance tasks completed")
                    
        except Exception as e:
            logging.error(f"❌ Maintenance task failed: {e}")
    
    def _clean_query_cache(self):
        """Clean expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, (data, timestamp) in self.query_cache.items()
            if current_time - timestamp > self.cache_ttl
        ]
        
        for key in expired_keys:
            del self.query_cache[key]
        
        if expired_keys:
            logging.info(f"🧹 Cleaned {len(expired_keys)} expired cache entries")
    
    def _clean_old_backups(self):
        """Clean old backup files"""
        # This would implement backup file cleanup logic
        # For now, just log the intention
        cutoff_date = datetime.now() - timedelta(days=self.backup_retention_days)
        logging.info(f"🧹 Would clean backups older than {cutoff_date}")
    
    def queue_auto_save(self, patient_data, callback=None, priority=1):
        """Enhanced auto-save queuing with priority"""
        try:
            # Higher priority for critical saves (lower number = higher priority)
            if self.auto_save_queue.qsize() < 50:  # Prevent memory buildup
                self.auto_save_queue.put((priority, (patient_data, callback)))
                return True
            else:
                logging.warning("⚠️ Auto-save queue full, rejecting save request")
                if callback:
                    callback(False, None, "Queue full")
                return False
                
        except Exception as e:
            logging.error(f"❌ Queue auto-save failed: {e}")
            if callback:
                callback(False, None, str(e))
            return False
    
    @retry_on_failure()
    def create_tables(self):
        """Create enhanced database schema with indexes and constraints"""
        with self.get_db_connection() as conn:
            with conn.cursor() as cursor:
                try:
                    # Enable required extensions
                    cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
                    
                    # Main patient table with enhanced constraints
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS patients (
                            patient_id VARCHAR(12) PRIMARY KEY,
                            assessment_date TIMESTAMP NOT NULL,
                            duration_minutes FLOAT CHECK (duration_minutes >= 0),
                            completion_percentage FLOAT CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
                            data_version VARCHAR(10) NOT NULL DEFAULT '2.0',
                            export_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            auto_save_count INTEGER DEFAULT 0,
                            data_checksum VARCHAR(64),
                            validation_status VARCHAR(20) DEFAULT 'pending',
                            validation_errors JSONB,
                            last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            is_active BOOLEAN DEFAULT TRUE,
                            
                            -- Add constraints
                            CONSTRAINT valid_patient_id CHECK (patient_id ~ '^PSY-[A-Z0-9]{8}$'),
                            CONSTRAINT valid_data_version CHECK (data_version IN ('1.0', '2.0', '2.1'))
                        )
                    """)
                    
                    # Enhanced triggers
                    cursor.execute("""
                        CREATE OR REPLACE FUNCTION update_updated_at_column()
                        RETURNS TRIGGER AS $$
                        BEGIN
                            NEW.updated_at = CURRENT_TIMESTAMP;
                            NEW.last_accessed = CURRENT_TIMESTAMP;
                            RETURN NEW;
                        END;
                        $$ language 'plpgsql';
                    """)
                    
                    cursor.execute("""
                        DROP TRIGGER IF EXISTS update_patients_updated_at ON patients;
                        CREATE TRIGGER update_patients_updated_at 
                            BEFORE UPDATE ON patients 
                            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
                    """)
                    
                    # Enhanced demographics table with validation
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS demographics (
                            patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                            age INTEGER CHECK (age >= 0 AND age <= 120),
                            gender VARCHAR(50),
                            sex_assigned VARCHAR(50),
                            marital_status VARCHAR(50),
                            children VARCHAR(20),
                            education VARCHAR(100),
                            occupation TEXT,
                            employment_status VARCHAR(50),
                            income_level VARCHAR(50),
                            insurance VARCHAR(50),
                            ethnicity JSONB,
                            primary_language VARCHAR(50),
                            interpreter_needed VARCHAR(50),
                            religion VARCHAR(50),
                            living_situation VARCHAR(50),
                            housing_stability VARCHAR(50),
                            emergency_contact JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            -- Validation constraints
                            CONSTRAINT valid_age CHECK (age IS NULL OR (age >= 0 AND age <= 120)),
                            CONSTRAINT valid_gender CHECK (gender IN ('Male', 'Female', 'Non-binary', 'Transgender male', 'Transgender female', 'Other', 'Prefer not to disclose')),
                            CONSTRAINT valid_emergency_contact CHECK (emergency_contact IS NULL OR jsonb_typeof(emergency_contact) = 'object')
                        )
                    """)
                    
                    # Enhanced chief complaint table
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS chief_complaint (
                            patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                            complaint TEXT NOT NULL,
                            presenting_problems JSONB,
                            symptom_duration VARCHAR(50),
                            symptom_onset VARCHAR(50),
                            referral_source VARCHAR(50),
                            referring_physician TEXT,
                            urgency_level VARCHAR(50),
                            previous_treatment VARCHAR(50),
                            treatment_goals TEXT,
                            recent_stressors JSONB,
                            precipitating_events TEXT,
                            functional_impact JSONB,
                            severity_rating INTEGER CHECK (severity_rating >= 1 AND severity_rating <= 10),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            -- Enhanced constraints
                            CONSTRAINT valid_urgency CHECK (urgency_level IN ('Routine', 'Urgent', 'Emergent')),
                            CONSTRAINT valid_severity CHECK (severity_rating IS NULL OR (severity_rating >= 1 AND severity_rating <= 10)),
                            CONSTRAINT non_empty_complaint CHECK (length(trim(complaint)) > 0)
                        )
                    """)
                    
                    # Enhanced risk assessment table with safety features
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS risk_assessment (
                            patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                            
                            -- Suicide risk fields
                            current_si VARCHAR(50),
                            si_frequency VARCHAR(50),
                            si_intensity INTEGER CHECK (si_intensity >= 1 AND si_intensity <= 10),
                            plan_details TEXT,
                            means_access VARCHAR(50),
                            protective_factors JSONB,
                            deterrents TEXT,
                            previous_attempts VARCHAR(50),
                            attempt_history JSONB,
                            family_suicide_history VARCHAR(50),
                            
                            -- Violence risk fields
                            homicidal_ideation VARCHAR(50),
                            violence_history VARCHAR(50),
                            target_identified VARCHAR(50),
                            target_details TEXT,
                            
                            -- Risk level and safety planning
                            calculated_risk_level VARCHAR(20),
                            clinical_risk_override VARCHAR(20),
                            risk_factors JSONB,
                            safety_plan_created BOOLEAN DEFAULT FALSE,
                            safety_plan_content TEXT,
                            hospitalization_considered VARCHAR(50),
                            
                            -- Metadata
                            risk_assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            assessed_by VARCHAR(100),
                            next_assessment_due DATE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            -- Safety constraints
                            CONSTRAINT valid_si_level CHECK (current_si IN ('None', 'Passive (wish to be dead)', 'Active without plan', 'Active with plan', 'Imminent risk')),
                            CONSTRAINT valid_risk_level CHECK (calculated_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
                            CONSTRAINT valid_means_access CHECK (means_access IN ('No access', 'Limited access', 'Easy access', 'Immediate access')),
                            CONSTRAINT high_risk_requires_plan CHECK (
                                (calculated_risk_level NOT IN ('High', 'Imminent')) OR 
                                (safety_plan_created = TRUE)
                            )
                        )
                    """)
                    
                    # Enhanced mental state examination table
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS mental_state_exam (
                            patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                            
                            -- Appearance and behavior
                            appearance JSONB,
                            behavior JSONB,
                            psychomotor_activity VARCHAR(50),
                            attitude VARCHAR(50),
                            eye_contact VARCHAR(50),
                            
                            -- Speech and language
                            speech_rate VARCHAR(50),
                            speech_volume VARCHAR(50),
                            speech_rhythm VARCHAR(50),
                            speech_abnormalities JSONB,
                            
                            -- Mood and affect
                            stated_mood VARCHAR(50),
                            observed_affect JSONB,
                            mood_affect_congruence VARCHAR(50),
                            mood_stability VARCHAR(50),
                            
                            -- Thought process and content
                            thought_process JSONB,
                            thought_content JSONB,
                            thought_form VARCHAR(50),
                            delusion_types JSONB,
                            obsessions_compulsions JSONB,
                            
                            -- Perceptions
                            hallucinations JSONB,
                            illusions JSONB,
                            perceptual_distortions JSONB,
                            hallucination_details TEXT,
                            
                            -- Cognition
                            orientation VARCHAR(50),
                            attention_concentration VARCHAR(50),
                            memory_immediate VARCHAR(50),
                            memory_recent VARCHAR(50),
                            memory_remote VARCHAR(50),
                            abstract_thinking VARCHAR(50),
                            
                            -- Insight and judgment
                            insight_level VARCHAR(50),
                            judgment_quality VARCHAR(50),
                            
                            -- Assessment metadata
                            mse_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            examiner VARCHAR(100),
                            examination_duration_minutes INTEGER,
                            reliability_assessment VARCHAR(50),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            -- Validation constraints
                            CONSTRAINT valid_insight CHECK (insight_level IN ('Good', 'Fair', 'Poor', 'Absent')),
                            CONSTRAINT valid_judgment CHECK (judgment_quality IN ('Good', 'Fair', 'Poor', 'Impaired')),
                            CONSTRAINT valid_orientation CHECK (orientation IN ('Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'))
                        )
                    """)
                    
                    # Enhanced substance use table with detailed tracking
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS substance_use (
                            patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                            
                            -- Alcohol assessment
                            alcohol_use_pattern VARCHAR(50),
                            drinks_per_week INTEGER CHECK (drinks_per_week >= 0),
                            binge_drinking_frequency VARCHAR(50),
                            cage_score INTEGER CHECK (cage_score >= 0 AND cage_score <= 4),
                            alcohol_age_onset INTEGER CHECK (alcohol_age_onset >= 0 AND alcohol_age_onset <= 80),
                            last_drink VARCHAR(50),
                            alcohol_treatment_history VARCHAR(50),
                            alcohol_consequences JSONB,
                            
                            -- Drug use assessment
                            substances_ever_used JSONB,
                            primary_substance VARCHAR(50),
                            current_use_status VARCHAR(50),
                            drug_age_onset INTEGER CHECK (drug_age_onset >= 0 AND drug_age_onset <= 80),
                            last_drug_use VARCHAR(50),
                            injection_history VARCHAR(50),
                            needle_sharing_history VARCHAR(50),
                            overdose_history VARCHAR(50),
                            
                            -- Impact assessment
                            substance_related_consequences JSONB,
                            functional_impairment VARCHAR(50),
                            treatment_motivation VARCHAR(50),
                            previous_treatment_attempts JSONB,
                            
                            -- Screening scores
                            audit_score INTEGER CHECK (audit_score >= 0 AND audit_score <= 40),
                            dast_score INTEGER CHECK (dast_score >= 0 AND dast_score <= 28),
                            
                            -- Metadata
                            assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            assessed_by VARCHAR(100),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            -- Validation constraints
                            CONSTRAINT valid_cage CHECK (cage_score IS NULL OR (cage_score >= 0 AND cage_score <= 4)),
                            CONSTRAINT valid_use_pattern CHECK (alcohol_use_pattern IN ('Never', 'Abstinent', 'Occasional', 'Regular', 'Heavy', 'Binge pattern')),
                            CONSTRAINT valid_injection_history CHECK (injection_history IN ('Never', 'Past use', 'Current use'))
                        )
                    """)
                    
                    # Create remaining core tables with similar enhancements
                    self._create_remaining_tables(cursor)
                    
                    # Create comprehensive indexes for performance
                    self._create_performance_indexes(cursor)
                    
                    # Create audit trail table
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS audit_trail (
                            audit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                            patient_id VARCHAR(12) REFERENCES patients(patient_id),
                            operation VARCHAR(20) NOT NULL,
                            table_name VARCHAR(50) NOT NULL,
                            old_values JSONB,
                            new_values JSONB,
                            changed_by VARCHAR(100),
                            change_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            session_info JSONB,
                            
                            CONSTRAINT valid_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'))
                        )
                    """)
                    
                    # Create data quality monitoring table
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS data_quality_metrics (
                            metric_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                            patient_id VARCHAR(12) REFERENCES patients(patient_id),
                            completeness_score FLOAT CHECK (completeness_score >= 0 AND completeness_score <= 100),
                            validation_errors JSONB,
                            validation_warnings JSONB,
                            data_freshness_hours INTEGER,
                            calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            
                            CONSTRAINT valid_completeness CHECK (completeness_score >= 0 AND completeness_score <= 100)
                        )
                    """)
                    
                    logging.info("✅ Enhanced database schema created successfully")
                    
                except Exception as e:
                    logging.error(f"❌ Error creating enhanced schema: {e}")
                    raise
    
    def _create_remaining_tables(self, cursor):
        """Create remaining tables with enhanced structure"""
        
        # Diagnostic formulation table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS diagnostic_formulation (
                patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                primary_diagnosis VARCHAR(200),
                primary_diagnosis_code VARCHAR(20),
                secondary_diagnoses JSONB,
                rule_out_diagnoses JSONB,
                diagnostic_certainty VARCHAR(50),
                dsm5_criteria_met TEXT,
                dsm5_criteria_not_met TEXT,
                icd11_codes JSONB,
                specifiers JSONB,
                severity_rating VARCHAR(50),
                course_modifiers JSONB,
                functional_impairment_level VARCHAR(50),
                differential_considerations TEXT,
                diagnostic_confidence INTEGER CHECK (diagnostic_confidence >= 1 AND diagnostic_confidence <= 10),
                formulation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                formulated_by VARCHAR(100),
                peer_review_status VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                CONSTRAINT valid_certainty CHECK (diagnostic_certainty IN ('Definite', 'Probable', 'Possible', 'Rule out', 'Deferred')),
                CONSTRAINT valid_impairment CHECK (functional_impairment_level IN ('None', 'Mild', 'Moderate', 'Severe', 'Profound'))
            )
        """)
        
        # Enhanced treatment planning table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS treatment_planning (
                patient_id VARCHAR(12) PRIMARY KEY REFERENCES patients(patient_id) ON DELETE CASCADE,
                
                -- Goals and objectives
                short_term_goals TEXT,
                long_term_goals TEXT,
                treatment_objectives JSONB,
                target_symptoms JSONB,
                functional_goals JSONB,
                
                -- Treatment modalities
                treatment_setting VARCHAR(50),
                treatment_intensity VARCHAR(50),
                psychotherapy_modalities JSONB,
                medication_plan TEXT,
                adjunctive_treatments JSONB,
                
                -- Scheduling and frequency
                session_frequency VARCHAR(50),
                estimated_duration VARCHAR(50),
                treatment_phases JSONB,
                
                -- Monitoring and measurement
                outcome_measures JSONB,
                progress_indicators JSONB,
                monitoring_schedule TEXT,
                
                -- Safety and risk management
                crisis_plan TEXT,
                safety_considerations JSONB,
                emergency_contacts JSONB,
                
                -- Collaborative elements
                family_involvement VARCHAR(50),
                care_team_members JSONB,
                referrals_needed JSONB,
                
                -- Barriers and resources
                treatment_barriers JSONB,
                patient_strengths JSONB,
                available_resources JSONB,
                
                -- Metadata
                plan_created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                approved_by VARCHAR(100),
                approval_date TIMESTAMP,
                next_review_date DATE,
                plan_status VARCHAR(50) DEFAULT 'Active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                CONSTRAINT valid_setting CHECK (treatment_setting IN ('Outpatient', 'Intensive outpatient', 'Partial hospitalization', 'Inpatient', 'Residential')),
                CONSTRAINT valid_plan_status CHECK (plan_status IN ('Draft', 'Active', 'Modified', 'Completed', 'Discontinued'))
            )
        """)
    
    def _create_performance_indexes(self, cursor):
        """Create comprehensive indexes for optimal performance"""
        indexes = [
            # Primary lookup indexes
            "CREATE INDEX IF NOT EXISTS idx_patients_assessment_date ON patients(assessment_date DESC)",
            "CREATE INDEX IF NOT EXISTS idx_patients_created_at ON patients(created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_patients_updated_at ON patients(updated_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_patients_active ON patients(is_active) WHERE is_active = TRUE",
            
            # Search and filtering indexes
            "CREATE INDEX IF NOT EXISTS idx_demographics_age ON demographics(age)",
            "CREATE INDEX IF NOT EXISTS idx_demographics_gender ON demographics(gender)",
            "CREATE INDEX IF NOT EXISTS idx_diagnostic_primary ON diagnostic_formulation(primary_diagnosis)",
            "CREATE INDEX IF NOT EXISTS idx_diagnostic_certainty ON diagnostic_formulation(diagnostic_certainty)",
            
            # Risk assessment indexes (critical for safety)
            "CREATE INDEX IF NOT EXISTS idx_risk_current_si ON risk_assessment(current_si)",
            "CREATE INDEX IF NOT EXISTS idx_risk_level ON risk_assessment(calculated_risk_level)",
            "CREATE INDEX IF NOT EXISTS idx_risk_safety_plan ON risk_assessment(safety_plan_created)",
            
            # Performance monitoring indexes
            "CREATE INDEX IF NOT EXISTS idx_patients_completion ON patients(completion_percentage)",
            "CREATE INDEX IF NOT EXISTS idx_patients_validation ON patients(validation_status)",
            "CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_trail(change_timestamp DESC)",
            
            # Compound indexes for common queries
            "CREATE INDEX IF NOT EXISTS idx_patients_status_date ON patients(is_active, assessment_date DESC)",
            "CREATE INDEX IF NOT EXISTS idx_demographics_age_gender ON demographics(age, gender)",
            
            # JSONB indexes for complex queries
            "CREATE INDEX IF NOT EXISTS idx_demographics_ethnicity_gin ON demographics USING GIN(ethnicity)",
            "CREATE INDEX IF NOT EXISTS idx_risk_factors_gin ON risk_assessment USING GIN(risk_factors)",
            "CREATE INDEX IF NOT EXISTS idx_substance_consequences_gin ON substance_use USING GIN(substance_related_consequences)",
            
            # Full-text search indexes
            "CREATE INDEX IF NOT EXISTS idx_chief_complaint_fts ON chief_complaint USING GIN(to_tsvector('english', complaint))",
            "CREATE INDEX IF NOT EXISTS idx_treatment_goals_fts ON treatment_planning USING GIN(to_tsvector('english', coalesce(short_term_goals, '') || ' ' || coalesce(long_term_goals, '')))"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                logging.warning(f"⚠️ Index creation warning: {e}")
        
        logging.info(f"✅ Created {len(indexes)} performance indexes")
    
    def _calculate_data_checksum(self, data):
        """Calculate checksum for data integrity verification"""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    @retry_on_failure()
    def _save_patient_data_sync(self, cursor, patient_data):
        """Enhanced synchronous save with data integrity checks"""
        if not isinstance(patient_data, dict):
            raise ValidationError("Invalid patient data format. Expected JSON object.")
        
        metadata = patient_data.get('metadata', {})
        clinical_data = patient_data.get('clinical_data', {})
        patient_id = metadata.get('patient_id', f"PSY-{str(uuid.uuid4())[:8].upper()}")
        
        # Calculate data checksum
        data_checksum = self._calculate_data_checksum(patient_data)
        
        # Validate required fields
        self._validate_patient_data(clinical_data, patient_id)
        
        # Check for duplicate or concurrent updates
        cursor.execute(
            "SELECT data_checksum, updated_at FROM patients WHERE patient_id = %s",
            (patient_id,)
        )
        existing = cursor.fetchone()
        
        if existing and existing['data_checksum'] == data_checksum:
            logging.info(f"⚡ No changes detected for {patient_id}, skipping save")
            return patient_id
        
        # Insert/update main patient record with enhanced metadata
        cursor.execute("""
            INSERT INTO patients (
                patient_id, assessment_date, duration_minutes, completion_percentage,
                data_version, export_timestamp, data_checksum, validation_status,
                validation_errors, auto_save_count
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (patient_id) DO UPDATE SET
                assessment_date = EXCLUDED.assessment_date,
                duration_minutes = EXCLUDED.duration_minutes,
                completion_percentage = EXCLUDED.completion_percentage,
                data_version = EXCLUDED.data_version,
                export_timestamp = EXCLUDED.export_timestamp,
                data_checksum = EXCLUDED.data_checksum,
                validation_status = EXCLUDED.validation_status,
                validation_errors = EXCLUDED.validation_errors,
                auto_save_count = patients.auto_save_count + 1,
                updated_at = CURRENT_TIMESTAMP
        """, (
            patient_id,
            metadata.get('assessment_date', datetime.now().isoformat()),
            metadata.get('duration_minutes'),
            metadata.get('completion_percentage', 0),
            metadata.get('data_version', '2.0'),
            metadata.get('export_timestamp', datetime.now().isoformat()),
            data_checksum,
            'validated' if not patient_data.get('validation_results', {}).get('errors') else 'has_errors',
            json.dumps(patient_data.get('validation_results', {}).get('errors', [])),
            1
        ))
        
        # Save clinical data to respective tables
        self._save_clinical_data_enhanced(cursor, patient_id, clinical_data, existing is not None)
        
        # Update data quality metrics
        self._update_data_quality_metrics(cursor, patient_id, patient_data)
        
        # Create audit trail entry
        self._create_audit_entry(cursor, patient_id, 'UPDATE' if existing else 'INSERT', 'patients', 
                               existing, {'patient_id': patient_id, 'checksum': data_checksum})
        
        return patient_id
    
    def _validate_patient_data(self, clinical_data, patient_id):
        """Enhanced data validation with detailed error reporting"""
        errors = []

        # Basic type validation
        if not isinstance(clinical_data, dict):
            raise ValidationError(f"Patient data must be a dictionary, got {type(clinical_data).__name__}")

        if not clinical_data:
            raise ValidationError("Patient data cannot be empty")

        # Check for at least one valid section
        valid_sections = ['demographics', 'risk_assessment', 'chief_complaint', 'diagnostic_formulation', 'treatment_plan']
        if not any(section in clinical_data for section in valid_sections):
            raise ValidationError(f"Patient data must contain at least one valid section: {', '.join(valid_sections)}")

        # Critical field validation
        if 'demographics' in clinical_data:
            demo = clinical_data['demographics']
            
            # Age validation
            if 'age' in demo:
                age = demo['age']
                if not isinstance(age, int) or age < 0 or age > 120:
                    errors.append(f"Invalid age: {age}")
            
            # Gender validation
            valid_genders = ['Male', 'Female', 'Non-binary', 'Transgender male', 'Transgender female', 'Other', 'Prefer not to disclose']
            if 'gender' in demo and demo['gender'] not in valid_genders:
                errors.append(f"Invalid gender: {demo['gender']}")
        
        # Risk assessment validation (critical for safety)
        if 'risk_assessment' in clinical_data:
            risk = clinical_data['risk_assessment']
            
            # Suicide ideation validation
            valid_si_levels = ['None', 'Passive (wish to be dead)', 'Active without plan', 'Active with plan', 'Imminent risk']
            if 'current_si' in risk and risk['current_si'] not in valid_si_levels:
                errors.append(f"Invalid suicide ideation level: {risk['current_si']}")
            
            # Safety plan requirement for high risk
            if risk.get('current_si') in ['Active with plan', 'Imminent risk']:
                if not risk.get('safety_plan_created'):
                    errors.append("Safety plan required for high suicide risk patients")
        
        # Diagnostic validation
        if 'diagnostic_formulation' in clinical_data:
            diag = clinical_data['diagnostic_formulation']
            
            # Primary diagnosis should not be empty
            if 'primary_diagnosis' in diag and not diag['primary_diagnosis'].strip():
                errors.append("Primary diagnosis cannot be empty")
        
        if errors:
            raise ValidationError(f"Patient data validation failed for {patient_id}: {'; '.join(errors)}")
    
    def _save_clinical_data_enhanced(self, cursor, patient_id, clinical_data, is_update):
        """Enhanced clinical data saving with batch operations"""
        
        # Use batch operations for better performance
        tables_to_update = []
        
        # Demographics
        if 'demographics' in clinical_data:
            demo_data = clinical_data['demographics']
            tables_to_update.append(('demographics', demo_data, [
                'age', 'gender', 'sex_assigned', 'marital_status', 'children',
                'education', 'occupation', 'employment_status', 'income_level', 'insurance',
                'ethnicity', 'primary_language', 'interpreter_needed', 'religion',
                'living_situation', 'housing_stability', 'emergency_contact'
            ]))
        
        # Chief complaint
        if 'chief_complaint' in clinical_data:
            cc_data = clinical_data['chief_complaint']
            tables_to_update.append(('chief_complaint', cc_data, [
                'complaint', 'presenting_problems', 'symptom_duration', 'symptom_onset',
                'referral_source', 'referring_physician', 'urgency_level', 'previous_treatment',
                'treatment_goals', 'recent_stressors', 'precipitating_events', 'functional_impact',
                'severity_rating'
            ]))
        
        # Risk assessment (high priority)
        if 'risk_assessment' in clinical_data:
            risk_data = clinical_data['risk_assessment']
            tables_to_update.append(('risk_assessment', risk_data, [
                'current_si', 'si_frequency', 'si_intensity', 'plan_details', 'means_access',
                'protective_factors', 'deterrents', 'previous_attempts', 'family_suicide_history',
                'homicidal_ideation', 'violence_history', 'target_identified', 'target_details',
                'calculated_risk_level', 'risk_factors', 'safety_plan_created', 'safety_plan_content',
                'hospitalization_considered'
            ]))
        
        # Mental state examination
        if 'mse' in clinical_data or 'mental_state_exam' in clinical_data:
            mse_data = clinical_data.get('mse', clinical_data.get('mental_state_exam', {}))
            tables_to_update.append(('mental_state_exam', mse_data, [
                'appearance', 'behavior', 'psychomotor_activity', 'attitude', 'eye_contact',
                'speech_rate', 'speech_volume', 'stated_mood', 'observed_affect', 'mood_affect_congruence',
                'thought_process', 'thought_content', 'hallucinations', 'orientation',
                'attention_concentration', 'memory_immediate', 'abstract_thinking', 'insight_level',
                'judgment_quality'
            ]))
        
        # Process all table updates
        for table_name, data, fields in tables_to_update:
            self._upsert_table_data(cursor, table_name, patient_id, data, fields, is_update)
    
    def _upsert_table_data(self, cursor, table_name, patient_id, data, fields, is_update):
        """Generic upsert operation for clinical data tables"""
        try:
            # Prepare field values, converting lists to JSON where needed
            field_values = []
            for field in fields:
                value = data.get(field)
                if isinstance(value, (list, dict)):
                    value = json.dumps(value)
                field_values.append(value)
            
            # Build the upsert query
            field_list = ', '.join(fields)
            value_placeholders = ', '.join(['%s'] * len(fields))
            update_clauses = ', '.join([f"{field} = EXCLUDED.{field}" for field in fields])
            
            upsert_sql = f"""
                INSERT INTO {table_name} (patient_id, {field_list})
                VALUES (%s, {value_placeholders})
                ON CONFLICT (patient_id) DO UPDATE SET {update_clauses}
            """
            
            cursor.execute(upsert_sql, [patient_id] + field_values)
            
        except Exception as e:
            logging.error(f"❌ Error upserting {table_name} for patient {patient_id}: {e}")
            raise
    
    def _update_data_quality_metrics(self, cursor, patient_id, patient_data):
        """Update data quality metrics for monitoring"""
        try:
            # Calculate completeness score
            total_fields = 50  # Approximate total critical fields
            completed_fields = 0
            
            clinical_data = patient_data.get('clinical_data', {})
            for section_data in clinical_data.values():
                if isinstance(section_data, dict):
                    completed_fields += len([v for v in section_data.values() if v])
            
            completeness_score = min(100, (completed_fields / total_fields) * 100)
            
            # Get validation results
            validation_results = patient_data.get('validation_results', {})
            validation_errors = validation_results.get('errors', [])
            validation_warnings = validation_results.get('warnings', [])
            
            # Calculate data freshness
            assessment_date = patient_data.get('metadata', {}).get('assessment_date')
            data_freshness_hours = 0
            if assessment_date:
                try:
                    assessment_dt = datetime.fromisoformat(assessment_date.replace('Z', '+00:00'))
                    data_freshness_hours = (datetime.now() - assessment_dt).total_seconds() / 3600
                except:
                    pass
            
            # Insert/update quality metrics
            cursor.execute("""
                INSERT INTO data_quality_metrics (
                    patient_id, completeness_score, validation_errors, validation_warnings,
                    data_freshness_hours
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (patient_id) DO UPDATE SET
                    completeness_score = EXCLUDED.completeness_score,
                    validation_errors = EXCLUDED.validation_errors,
                    validation_warnings = EXCLUDED.validation_warnings,
                    data_freshness_hours = EXCLUDED.data_freshness_hours,
                    calculated_at = CURRENT_TIMESTAMP
            """, (
                patient_id,
                completeness_score,
                json.dumps(validation_errors),
                json.dumps(validation_warnings),
                data_freshness_hours
            ))
            
        except Exception as e:
            logging.warning(f"⚠️ Failed to update quality metrics for {patient_id}: {e}")
    
    def _create_audit_entry(self, cursor, patient_id, operation, table_name, old_values, new_values):
        """Create audit trail entry"""
        try:
            cursor.execute("""
                INSERT INTO audit_trail (
                    patient_id, operation, table_name, old_values, new_values,
                    changed_by, session_info
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                patient_id,
                operation,
                table_name,
                json.dumps(old_values, default=str) if old_values else None,
                json.dumps(new_values, default=str) if new_values else None,
                'system_user',  # Could be enhanced to capture actual user
                json.dumps({'source': 'psychiatric_assessment_app', 'version': '2.0'})
            ))
        except Exception as e:
            logging.warning(f"⚠️ Failed to create audit entry: {e}")
    
    @retry_on_failure()
    def insert_patient_data(self, patient_data):
        """Enhanced synchronous patient data insertion with comprehensive validation"""
        try:
            start_time = time.time()
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    patient_id = self._save_patient_data_sync(cursor, patient_data)
                    
                    processing_time = time.time() - start_time
                    self.query_performance_log.append({
                        'operation': 'insert_patient_data',
                        'patient_id': patient_id,
                        'processing_time': processing_time,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    # Log performance warnings
                    if processing_time > 5.0:
                        logging.warning(f"⚠️ Slow patient data insert: {processing_time:.2f}s for {patient_id}")
                    
                    logging.info(f"✅ Patient data inserted successfully: {patient_id} in {processing_time:.2f}s")
                    return patient_id
                    
        except ValidationError as e:
            logging.error(f"❌ Patient data validation failed: {e}")
            raise
        except Exception as e:
            logging.error(f"❌ Error inserting patient data: {e}")
            raise DatabaseError(f"Failed to insert patient data: {str(e)}")
    
    @retry_on_failure()
    def get_all_patients(self, limit=100, offset=0, search_term=None, filters=None):
        """Enhanced patient retrieval with advanced filtering and caching"""
        try:
            # Create cache key
            cache_key = f"patients_{limit}_{offset}_{search_term}_{str(filters)}"
            
            # Check cache first
            if cache_key in self.query_cache:
                cached_data, timestamp = self.query_cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    return cached_data
                else:
                    del self.query_cache[cache_key]
            
            start_time = time.time()
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Build enhanced query with joins and filtering
                    base_query = """
                        SELECT 
                            p.patient_id, 
                            p.assessment_date,
                            p.completion_percentage,
                            p.validation_status,
                            p.created_at, 
                            p.updated_at, 
                            p.auto_save_count,
                            p.data_checksum,
                            
                            -- Demographics
                            d.age, 
                            d.gender,
                            d.primary_language,
                            d.insurance,
                            
                            -- Clinical summary
                            df.primary_diagnosis,
                            df.diagnostic_certainty,
                            
                            -- Risk assessment summary
                            ra.current_si,
                            ra.calculated_risk_level,
                            ra.safety_plan_created,
                            
                            -- Data quality
                            dqm.completeness_score,
                            dqm.data_freshness_hours
                            
                        FROM patients p
                        LEFT JOIN demographics d ON p.patient_id = d.patient_id
                        LEFT JOIN diagnostic_formulation df ON p.patient_id = df.patient_id
                        LEFT JOIN risk_assessment ra ON p.patient_id = ra.patient_id
                        LEFT JOIN data_quality_metrics dqm ON p.patient_id = dqm.patient_id
                        WHERE p.is_active = TRUE
                    """
                    
                    params = []
                    
                    # Add search functionality
                    if search_term:
                        search_conditions = """
                            AND (
                                p.patient_id ILIKE %s 
                                OR df.primary_diagnosis ILIKE %s
                                OR d.gender ILIKE %s
                                OR CAST(d.age AS TEXT) ILIKE %s
                            )
                        """
                        base_query += search_conditions
                        search_param = f"%{search_term}%"
                        params.extend([search_param, search_param, search_param, search_param])
                    
                    # Add filters
                    if filters:
                        if filters.get('risk_level'):
                            base_query += " AND ra.calculated_risk_level = %s"
                            params.append(filters['risk_level'])
                        
                        if filters.get('diagnosis_certainty'):
                            base_query += " AND df.diagnostic_certainty = %s"
                            params.append(filters['diagnosis_certainty'])
                        
                        if filters.get('age_range'):
                            min_age, max_age = filters['age_range']
                            base_query += " AND d.age BETWEEN %s AND %s"
                            params.extend([min_age, max_age])
                        
                        if filters.get('completion_threshold'):
                            base_query += " AND p.completion_percentage >= %s"
                            params.append(filters['completion_threshold'])
                    
                    # Add ordering and pagination
                    base_query += """
                        ORDER BY p.updated_at DESC
                        LIMIT %s OFFSET %s
                    """
                    params.extend([limit, offset])
                    
                    cursor.execute(base_query, params)
                    results = cursor.fetchall()
                    
                    # Convert to list of dicts for JSON serialization
                    patients = [dict(row) for row in results]
                    
                    # Cache the results
                    if len(self.query_cache) < self.cache_max_size:
                        self.query_cache[cache_key] = (patients, time.time())
                    
                    processing_time = time.time() - start_time
                    logging.info(f"✅ Retrieved {len(patients)} patients in {processing_time:.2f}s")
                    
                    return patients
                    
        except Exception as e:
            logging.error(f"❌ Error retrieving patients: {e}")
            raise DatabaseError(f"Failed to retrieve patients: {str(e)}")
    
    @retry_on_failure()
    def get_patient_data(self, patient_id):
        """Enhanced patient data retrieval with comprehensive joins"""
        try:
            cache_key = f"patient_data_{patient_id}"
            
            # Check cache
            if cache_key in self.query_cache:
                cached_data, timestamp = self.query_cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    return cached_data
            
            start_time = time.time()
            patient_data = {'metadata': {}, 'clinical_data': {}}
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Get patient metadata
                    cursor.execute("""
                        SELECT p.*, dqm.completeness_score, dqm.validation_errors, dqm.validation_warnings
                        FROM patients p
                        LEFT JOIN data_quality_metrics dqm ON p.patient_id = dqm.patient_id
                        WHERE p.patient_id = %s AND p.is_active = TRUE
                    """, (patient_id,))
                    
                    patient_record = cursor.fetchone()
                    
                    if not patient_record:
                        logging.warning(f"⚠️ No active patient found: {patient_id}")
                        return None
                    
                    patient_data['metadata'] = dict(patient_record)
                    
                    # Enhanced clinical data retrieval with single query approach
                    clinical_tables = [
                        'demographics', 'chief_complaint', 'risk_assessment', 'mental_state_exam',
                        'substance_use', 'diagnostic_formulation', 'treatment_planning'
                    ]
                    
                    # Use batch retrieval for better performance
                    for table_name in clinical_tables:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE patient_id = %s", (patient_id,))
                        record = cursor.fetchone()
                        if record:
                            patient_data['clinical_data'][table_name] = self._deserialize_jsonb_fields(dict(record))
                    
                    # Get audit trail for the patient (last 10 entries)
                    cursor.execute("""
                        SELECT operation, table_name, change_timestamp, changed_by
                        FROM audit_trail 
                        WHERE patient_id = %s 
                        ORDER BY change_timestamp DESC 
                        LIMIT 10
                    """, (patient_id,))
                    
                    audit_entries = [dict(row) for row in cursor.fetchall()]
                    patient_data['audit_trail'] = audit_entries
                    
                    # Cache the result
                    if len(self.query_cache) < self.cache_max_size:
                        self.query_cache[cache_key] = (patient_data, time.time())
                    
                    processing_time = time.time() - start_time
                    logging.info(f"✅ Retrieved patient data for {patient_id} in {processing_time:.2f}s")
                    
                    return patient_data
                    
        except Exception as e:
            logging.error(f"❌ Error retrieving patient {patient_id}: {e}")
            raise DatabaseError(f"Failed to retrieve patient data: {str(e)}")
    
    def _deserialize_jsonb_fields(self, record):
        """Enhanced JSONB deserialization with error handling"""
        jsonb_fields = {
            # Demographics
            'ethnicity', 'emergency_contact',
            
            # Chief complaint
            'presenting_problems', 'recent_stressors', 'functional_impact',
            
            # Risk assessment
            'protective_factors', 'risk_factors',
            
            # Mental state exam
            'appearance', 'behavior', 'observed_affect', 'thought_process', 'thought_content',
            'hallucinations', 'speech_abnormalities',
            
            # Substance use
            'substances_ever_used', 'alcohol_consequences', 'substance_related_consequences',
            'previous_treatment_attempts',
            
            # Diagnostic formulation
            'secondary_diagnoses', 'rule_out_diagnoses', 'specifiers', 'course_modifiers',
            'icd11_codes',
            
            # Treatment planning
            'treatment_objectives', 'target_symptoms', 'functional_goals', 'psychotherapy_modalities',
            'adjunctive_treatments', 'outcome_measures', 'progress_indicators',
            'safety_considerations', 'care_team_members', 'treatment_barriers'
        }
        
        for field in jsonb_fields:
            if field in record and record[field] is not None:
                try:
                    if isinstance(record[field], str):
                        record[field] = json.loads(record[field])
                except (json.JSONDecodeError, TypeError) as e:
                    logging.warning(f"⚠️ Failed to deserialize {field}: {e}")
                    # Keep original value as fallback
        
        return record
    
    @retry_on_failure()
    def get_patients_by_risk_level(self, risk_level, include_details=False):
        """Get patients by suicide risk level for clinical monitoring"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    if include_details:
                        query = """
                            SELECT 
                                p.patient_id,
                                p.assessment_date,
                                d.age,
                                d.gender,
                                ra.current_si,
                                ra.calculated_risk_level,
                                ra.safety_plan_created,
                                ra.risk_assessment_date,
                                ra.next_assessment_due,
                                df.primary_diagnosis
                            FROM patients p
                            JOIN risk_assessment ra ON p.patient_id = ra.patient_id
                            LEFT JOIN demographics d ON p.patient_id = d.patient_id
                            LEFT JOIN diagnostic_formulation df ON p.patient_id = df.patient_id
                            WHERE ra.calculated_risk_level = %s 
                                AND p.is_active = TRUE
                            ORDER BY ra.risk_assessment_date DESC
                        """
                    else:
                        query = """
                            SELECT patient_id, calculated_risk_level, safety_plan_created
                            FROM risk_assessment ra
                            JOIN patients p ON ra.patient_id = p.patient_id
                            WHERE ra.calculated_risk_level = %s 
                                AND p.is_active = TRUE
                        """
                    
                    cursor.execute(query, (risk_level,))
                    return [dict(row) for row in cursor.fetchall()]
                    
        except Exception as e:
            logging.error(f"❌ Error retrieving patients by risk level {risk_level}: {e}")
            raise DatabaseError(f"Failed to retrieve high-risk patients: {str(e)}")
    
    @retry_on_failure()
    def get_database_statistics(self):
        """Get comprehensive database statistics for monitoring"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    stats = {}
                    
                    # Patient counts
                    cursor.execute("""
                        SELECT 
                            COUNT(*) as total_patients,
                            COUNT(*) FILTER (WHERE is_active = TRUE) as active_patients,
                            COUNT(*) FILTER (WHERE validation_status = 'validated') as validated_patients,
                            AVG(completion_percentage) as avg_completion,
                            COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as patients_this_week,
                            COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as patients_this_month
                        FROM patients
                    """)
                    stats['patients'] = dict(cursor.fetchone())
                    
                    # Risk level distribution
                    cursor.execute("""
                        SELECT 
                            calculated_risk_level,
                            COUNT(*) as count,
                            COUNT(*) FILTER (WHERE safety_plan_created = TRUE) as with_safety_plan
                        FROM risk_assessment ra
                        JOIN patients p ON ra.patient_id = p.patient_id
                        WHERE p.is_active = TRUE
                        GROUP BY calculated_risk_level
                    """)
                    stats['risk_levels'] = {row['calculated_risk_level']: dict(row) for row in cursor.fetchall()}
                    
                    # Most common diagnoses
                    cursor.execute("""
                        SELECT primary_diagnosis, COUNT(*) as count
                        FROM diagnostic_formulation df
                        JOIN patients p ON df.patient_id = p.patient_id
                        WHERE p.is_active = TRUE AND primary_diagnosis IS NOT NULL
                        GROUP BY primary_diagnosis
                        ORDER BY count DESC
                        LIMIT 10
                    """)
                    stats['top_diagnoses'] = [dict(row) for row in cursor.fetchall()]
                    
                    # Data quality metrics
                    cursor.execute("""
                        SELECT 
                            AVG(completeness_score) as avg_completeness,
                            COUNT(*) FILTER (WHERE completeness_score >= 80) as high_quality_records,
                            AVG(data_freshness_hours) as avg_freshness_hours
                        FROM data_quality_metrics dqm
                        JOIN patients p ON dqm.patient_id = p.patient_id
                        WHERE p.is_active = TRUE
                    """)
                    stats['data_quality'] = dict(cursor.fetchone())
                    
                    # Database performance metrics
                    stats['performance'] = {
                        'total_connections': self.connection_metrics['total_connections'],
                        'failed_connections': self.connection_metrics['failed_connections'],
                        'avg_query_time': self.connection_metrics['avg_query_time'],
                        'slow_queries': self.connection_metrics['slow_queries'],
                        'cache_hit_rate': len(self.query_cache) / max(1, self.connection_metrics['total_connections'])
                    }
                    
                    return stats
                    
        except Exception as e:
            logging.error(f"❌ Error getting database statistics: {e}")
            raise DatabaseError(f"Failed to retrieve database statistics: {str(e)}")
    
    @retry_on_failure()
    def backup_patient_data(self, patient_id, backup_location=None):
        """Create a backup of patient data"""
        try:
            patient_data = self.get_patient_data(patient_id)
            if not patient_data:
                raise ValueError(f"No data found for patient {patient_id}")
            
            backup_data = {
                'backup_timestamp': datetime.now().isoformat(),
                'backup_version': '2.0',
                'patient_data': patient_data,
                'checksum': self._calculate_data_checksum(patient_data)
            }
            
            if backup_location:
                backup_filename = f"{backup_location}/backup_{patient_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(backup_filename, 'w') as f:
                    json.dump(backup_data, f, indent=2, default=str)
                logging.info(f"✅ Patient data backed up to {backup_filename}")
                return backup_filename
            else:
                return backup_data
                
        except Exception as e:
            logging.error(f"❌ Error backing up patient {patient_id}: {e}")
            raise DatabaseError(f"Failed to backup patient data: {str(e)}")
    
    def close_connections(self):
        """Enhanced cleanup with proper thread management"""
        try:
            # Stop auto-save worker
            self.auto_save_running = False
            
            # Signal auto-save thread to stop
            if self.auto_save_queue:
                try:
                    self.auto_save_queue.put((0, None), timeout=5)  # High priority shutdown signal
                except queue.Full:
                    pass
            
            # Wait for auto-save thread to finish
            if self.auto_save_thread and self.auto_save_thread.is_alive():
                self.auto_save_thread.join(timeout=10)
                if self.auto_save_thread.is_alive():
                    logging.warning("⚠️ Auto-save thread did not shut down cleanly")
            
            # Clear caches
            self.query_cache.clear()
            self.query_performance_log.clear()
            
            # Close connection pool
            if self.pool:
                self.pool.closeall()
                logging.info("[OK] Database connections closed cleanly")
                
        except Exception as e:
            logging.error(f"❌ Error during database cleanup: {e}")
    
    def __del__(self):
        """Enhanced destructor"""
        try:
            self.close_connections()
        except:
            pass  # Ignore errors during cleanup

# Enhanced connection factory for dependency injection
class DatabaseConnectionFactory:
    """Factory for creating database connections with different configurations"""
    
    @staticmethod
    def create_connection(config_name='default'):
        """Create database connection based on configuration"""
        configs = {
            'default': {
                'host': os.environ.get('DB_HOST', 'localhost'),
                'database': os.environ.get('DB_NAME', 'psychiatric_assessments'),
                'user': os.environ.get('DB_USER', 'postgres'),
                'password': os.environ.get('DB_PASSWORD', 'password'),
                'port': int(os.environ.get('DB_PORT', 5432)),
            },
            'test': {
                'host': os.environ.get('TEST_DB_HOST', 'localhost'),
                'database': os.environ.get('TEST_DB_NAME', 'psychiatric_test'),
                'user': os.environ.get('TEST_DB_USER', 'postgres'),
                'password': os.environ.get('TEST_DB_PASSWORD', 'password'),
                'port': int(os.environ.get('TEST_DB_PORT', 5432)),
            }
        }
        
        if config_name not in configs:
            raise ValueError(f"Unknown configuration: {config_name}")
        
        # Create database instance with specific config
        db = PsychiatricAssessmentDB()
        db.db_params.update(configs[config_name])
        return db

# Module-level convenience functions
def get_database_instance():
    """Get singleton database instance"""
    if not hasattr(get_database_instance, '_instance'):
        get_database_instance._instance = PsychiatricAssessmentDB()
    return get_database_instance._instance

def create_test_database():
    """Create test database instance"""
    return DatabaseConnectionFactory.create_connection('test')