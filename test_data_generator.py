#!/usr/bin/env python3
"""
Comprehensive Test Data Generator for Psychiatric Assessment Application
Creates realistic clinical test data for testing all form sections and database operations.
"""

import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any

class PsychiatricTestDataGenerator:
    """Generates comprehensive test data for psychiatric assessments"""
    
    def __init__(self):
        self.test_scenarios = [
            "major_depression",
            "bipolar_disorder", 
            "anxiety_disorder",
            "psychotic_disorder",
            "substance_use_disorder"
        ]
        
    def generate_demographics_data(self, scenario: str) -> Dict[str, Any]:
        """Generate realistic demographics data"""
        scenarios = {
            "major_depression": {
                "age": random.randint(25, 65),
                "gender": random.choice(["Female", "Male"]),
                "marital_status": random.choice(["Divorced", "Single, never married", "Married"]),
                "education": random.choice(["Bachelor's degree", "High school/GED", "Some college"]),
                "employment_status": random.choice(["Unemployed", "Employed part-time", "Disabled"])
            },
            "bipolar_disorder": {
                "age": random.randint(18, 45),
                "gender": random.choice(["Male", "Female", "Non-binary"]),
                "marital_status": random.choice(["Single, never married", "Divorced", "Married"]),
                "education": random.choice(["Some college", "Bachelor's degree", "High school/GED"]),
                "employment_status": random.choice(["Employed full-time", "Unemployed", "Student"])
            },
            "anxiety_disorder": {
                "age": random.randint(20, 50),
                "gender": random.choice(["Female", "Male"]),
                "marital_status": random.choice(["Married", "Single, never married"]),
                "education": random.choice(["Bachelor's degree", "Master's degree", "Some college"]),
                "employment_status": random.choice(["Employed full-time", "Employed part-time"])
            },
            "psychotic_disorder": {
                "age": random.randint(18, 35),
                "gender": random.choice(["Male", "Female"]),
                "marital_status": random.choice(["Single, never married", "Divorced"]),
                "education": random.choice(["High school/GED", "Some college", "Less than high school"]),
                "employment_status": random.choice(["Unemployed", "Disabled", "Student"])
            },
            "substance_use_disorder": {
                "age": random.randint(22, 55),
                "gender": random.choice(["Male", "Female"]),
                "marital_status": random.choice(["Divorced", "Single, never married", "Separated"]),
                "education": random.choice(["High school/GED", "Some college", "Bachelor's degree"]),
                "employment_status": random.choice(["Unemployed", "Employed part-time", "Employed full-time"])
            }
        }
        
        base_data = scenarios.get(scenario, scenarios["major_depression"])
        base_data.update({
            "occupation": self._get_occupation(base_data["employment_status"]),
            "primary_language": "English",
            "religion": random.choice(["Christian", "Agnostic", "Other", "Prefer not to disclose"]),
            "ethnicity": random.choice(["White/Caucasian", "Black/African American", "Hispanic/Latino", "Asian", "Mixed race"])
        })
        
        return base_data
    
    def generate_chief_complaint_data(self, scenario: str) -> Dict[str, Any]:
        """Generate chief complaint data based on scenario"""
        complaints = {
            "major_depression": {
                "presenting_problem": "I've been feeling really down and hopeless for the past few months. I can't seem to enjoy anything anymore and I'm having trouble sleeping.",
                "symptom_duration": "3-6 months",
                "symptom_onset": "Gradual",
                "urgency_level": "Urgent",
                "referral_source": "Primary care",
                "previous_treatment": "None"
            },
            "bipolar_disorder": {
                "presenting_problem": "I've been having extreme mood swings. Sometimes I feel on top of the world and don't need sleep, other times I crash into deep depression.",
                "symptom_duration": "6-12 months", 
                "symptom_onset": "Chronic with acute exacerbation",
                "urgency_level": "Urgent",
                "referral_source": "Emergency dept",
                "previous_treatment": "Medication only"
            },
            "anxiety_disorder": {
                "presenting_problem": "I've been having panic attacks and constant worry about everything. It's affecting my work and relationships.",
                "symptom_duration": "1-3 months",
                "symptom_onset": "Acute (sudden)",
                "urgency_level": "Routine",
                "referral_source": "Self-referral",
                "previous_treatment": "Counseling only"
            },
            "psychotic_disorder": {
                "presenting_problem": "I've been hearing voices and feeling like people are watching me. My family is worried about my behavior.",
                "symptom_duration": "2-4 weeks",
                "symptom_onset": "Acute (sudden)",
                "urgency_level": "Emergent",
                "referral_source": "Family",
                "previous_treatment": "Hospitalization"
            },
            "substance_use_disorder": {
                "presenting_problem": "My drinking has gotten out of control. I've lost my job and my family is threatening to leave me.",
                "symptom_duration": "> 1 year",
                "symptom_onset": "Gradual",
                "urgency_level": "Urgent",
                "referral_source": "Court-ordered",
                "previous_treatment": "Multiple providers"
            }
        }
        
        return complaints.get(scenario, complaints["major_depression"])
    
    def generate_risk_assessment_data(self, scenario: str) -> Dict[str, Any]:
        """Generate risk assessment data"""
        risk_levels = {
            "major_depression": {
                "current_si": "Active without plan",
                "si_frequency": "Daily",
                "means_access": "Limited access",
                "homicidal_ideation": "None",
                "violence_history": "None"
            },
            "bipolar_disorder": {
                "current_si": "Passive (wish to be dead)",
                "si_frequency": "Occasional", 
                "means_access": "No access",
                "homicidal_ideation": "None",
                "violence_history": "Verbal threats"
            },
            "anxiety_disorder": {
                "current_si": "None",
                "homicidal_ideation": "None",
                "violence_history": "None"
            },
            "psychotic_disorder": {
                "current_si": "Active with plan",
                "si_frequency": "Constant",
                "means_access": "Easy access",
                "homicidal_ideation": "Specific thoughts",
                "violence_history": "Physical violence"
            },
            "substance_use_disorder": {
                "current_si": "Passive (wish to be dead)",
                "si_frequency": "Rare",
                "means_access": "Limited access",
                "homicidal_ideation": "None",
                "violence_history": "Property damage"
            }
        }
        
        return risk_levels.get(scenario, risk_levels["major_depression"])
    
    def generate_clinical_scales_data(self, scenario: str) -> Dict[str, Any]:
        """Generate clinical scales and ratings data"""
        scales = {
            "major_depression": {
                "phq9_score": random.randint(15, 23),
                "phq9_interpretation": "Moderately severe depression (15-19)",
                "gad7_score": random.randint(8, 14),
                "gad7_interpretation": "Moderate anxiety (10-14)",
                "gaf_score": random.randint(41, 50)
            },
            "bipolar_disorder": {
                "phq9_score": random.randint(10, 18),
                "phq9_interpretation": "Moderate depression (10-14)",
                "gad7_score": random.randint(5, 12),
                "gad7_interpretation": "Mild anxiety (5-9)",
                "gaf_score": random.randint(31, 60)
            },
            "anxiety_disorder": {
                "phq9_score": random.randint(5, 12),
                "phq9_interpretation": "Mild depression (5-9)",
                "gad7_score": random.randint(15, 21),
                "gad7_interpretation": "Severe anxiety (15-21)",
                "gaf_score": random.randint(51, 70)
            },
            "psychotic_disorder": {
                "phq9_score": random.randint(8, 16),
                "phq9_interpretation": "Moderate depression (10-14)",
                "gad7_score": random.randint(6, 15),
                "gad7_interpretation": "Moderate anxiety (10-14)",
                "gaf_score": random.randint(21, 40)
            },
            "substance_use_disorder": {
                "phq9_score": random.randint(12, 20),
                "phq9_interpretation": "Moderately severe depression (15-19)",
                "gad7_score": random.randint(7, 16),
                "gad7_interpretation": "Moderate anxiety (10-14)",
                "gaf_score": random.randint(31, 50)
            }
        }
        
        return scales.get(scenario, scales["major_depression"])
    
    def _get_occupation(self, employment_status: str) -> str:
        """Get appropriate occupation based on employment status"""
        if employment_status == "Unemployed":
            return "Unemployed"
        elif employment_status == "Student":
            return "Student"
        elif employment_status == "Retired":
            return "Retired"
        elif employment_status == "Disabled":
            return "Unable to work"
        else:
            occupations = ["Teacher", "Nurse", "Engineer", "Sales Associate", "Manager", 
                          "Technician", "Administrative Assistant", "Construction Worker"]
            return random.choice(occupations)
    
    def generate_complete_assessment(self, scenario: str) -> Dict[str, Any]:
        """Generate a complete psychiatric assessment"""
        assessment = {
            "scenario": scenario,
            "patient_id": f"TEST_{scenario.upper()}_{random.randint(1000, 9999)}",
            "assessment_date": datetime.now().isoformat(),
            "demographics": self.generate_demographics_data(scenario),
            "chief_complaint": self.generate_chief_complaint_data(scenario),
            "risk_assessment": self.generate_risk_assessment_data(scenario),
            "clinical_scales": self.generate_clinical_scales_data(scenario)
        }
        
        return assessment
    
    def generate_test_suite(self) -> List[Dict[str, Any]]:
        """Generate complete test suite with all scenarios"""
        test_suite = []
        
        for scenario in self.test_scenarios:
            # Generate 2 test cases per scenario
            for i in range(2):
                assessment = self.generate_complete_assessment(scenario)
                assessment["test_case_id"] = f"{scenario}_{i+1}"
                test_suite.append(assessment)
        
        return test_suite

def main():
    """Generate and save test data"""
    generator = PsychiatricTestDataGenerator()
    test_suite = generator.generate_test_suite()
    
    # Save to JSON file
    with open("psychiatric_test_data.json", "w") as f:
        json.dump(test_suite, f, indent=2)
    
    print(f"Generated {len(test_suite)} test cases")
    print("Test scenarios included:")
    for scenario in generator.test_scenarios:
        count = len([t for t in test_suite if t["scenario"] == scenario])
        print(f"  - {scenario}: {count} cases")
    
    print("\nTest data saved to 'psychiatric_test_data.json'")

if __name__ == "__main__":
    main()
