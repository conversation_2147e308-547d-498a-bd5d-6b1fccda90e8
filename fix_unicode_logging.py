#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix Unicode characters in logging messages for Windows compatibility
"""

import re

def fix_unicode_in_file(filename):
    """Replace Unicode characters with ASCII equivalents"""
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace Unicode characters
    replacements = {
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '💡': '[INFO]'
    }
    
    for unicode_char, ascii_replacement in replacements.items():
        content = content.replace(unicode_char, ascii_replacement)
    
    # Write back to file
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed Unicode characters in {filename}")

if __name__ == "__main__":
    fix_unicode_in_file("database.py")
    fix_unicode_in_file("psychiatric_assessment_app.py")
    print("Unicode fix complete!")
