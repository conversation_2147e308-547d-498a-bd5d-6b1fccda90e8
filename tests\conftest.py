"""
Comprehensive test fixtures and configuration for psychiatric assessment application testing.
Provides database mocking, test data generation, and utility functions for all test modules.
"""

import pytest
import os
import json
import tempfile
import shutil
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import uuid

# Import application modules
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import PsychiatricAssessmentDB, DatabaseError, ValidationError
from test_data_generator import PsychiatricTestDataGenerator

# Test configuration
TEST_DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'database': os.environ.get('DB_NAME', 'psychiatric_test_db'),
    'user': os.environ.get('DB_USER', 'postgres'),
    'password': os.environ.get('DB_PASSWORD', 'test_password'),
    'port': int(os.environ.get('DB_PORT', 5432))
}

@pytest.fixture(scope="session")
def test_db_config():
    """Provide test database configuration"""
    return TEST_DB_CONFIG.copy()

@pytest.fixture(scope="session")
def test_data_generator():
    """Provide test data generator instance"""
    return PsychiatricTestDataGenerator()

@pytest.fixture(scope="function")
def mock_db_connection():
    """Mock database connection for unit tests"""
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    # Configure cursor mock
    mock_cursor.fetchone.return_value = None
    mock_cursor.fetchall.return_value = []
    mock_cursor.execute.return_value = None
    mock_cursor.__enter__.return_value = mock_cursor
    mock_cursor.__exit__.return_value = None

    # Configure connection mock
    mock_conn.cursor.return_value = mock_cursor
    mock_conn.__enter__.return_value = mock_conn
    mock_conn.__exit__.return_value = None
    mock_conn.commit.return_value = None
    mock_conn.rollback.return_value = None
    mock_conn.closed = 0

    return mock_conn, mock_cursor

@pytest.fixture(scope="function")
def mock_db_pool(mock_db_connection):
    """Mock database connection pool"""
    mock_pool = MagicMock()
    mock_conn, mock_cursor = mock_db_connection

    mock_pool.getconn.return_value = mock_conn
    mock_pool.putconn.return_value = None

    return mock_pool

@pytest.fixture(scope="function")
def mock_psychiatric_db(mock_db_pool):
    """Mock PsychiatricAssessmentDB instance"""
    with patch('database.psycopg2.pool.ThreadedConnectionPool') as mock_pool_class:
        mock_pool_class.return_value = mock_db_pool

        db = PsychiatricAssessmentDB(skip_init=True)
        db.pool = mock_db_pool

        return db

@pytest.fixture(scope="function")
def sample_patient_data(test_data_generator):
    """Generate sample patient data for testing"""
    return test_data_generator.generate_complete_assessment("major_depression")

@pytest.fixture(scope="function")
def sample_demographics_data():
    """Sample demographics data for testing"""
    return {
        "age": 35,
        "gender": "Female",
        "sex_assigned": "Female",
        "marital_status": "Married",
        "children": "2",
        "education": "Bachelor's degree",
        "occupation": "Teacher",
        "employment_status": "Employed full-time",
        "income_level": "$50,000-$75,000",
        "insurance": "Private insurance",
        "ethnicity": ["White/Caucasian"],
        "primary_language": "English",
        "interpreter_needed": "No",
        "religion": "Christian",
        "living_situation": "Lives with family",
        "housing_stability": "Stable housing",
        "emergency_contact": {
            "name": "John Doe",
            "relationship": "Spouse",
            "phone": "555-0123"
        }
    }

@pytest.fixture(scope="function")
def sample_risk_assessment_data():
    """Sample risk assessment data for testing"""
    return {
        "current_si": "Active without plan",
        "si_frequency": "Daily",
        "si_intensity": 7,
        "plan_details": "No specific plan described",
        "means_access": "Limited access",
        "protective_factors": ["Family support", "Religious beliefs"],
        "deterrents": "Concern for children",
        "previous_attempts": "None",
        "attempt_history": [],
        "family_suicide_history": "None",
        "homicidal_ideation": "None",
        "violence_history": "None",
        "target_identified": "No",
        "target_details": "",
        "calculated_risk_level": "Moderate",
        "clinical_risk_override": "",
        "risk_factors": ["Depression", "Social isolation"],
        "safety_plan_created": True,
        "safety_plan_content": "Remove means, contact support person, go to ER if needed",
        "hospitalization_considered": "No"
    }

@pytest.fixture(scope="function")
def invalid_patient_data():
    """Invalid patient data for error testing"""
    return {
        "demographics": {
            "age": -5,  # Invalid age
            "gender": "InvalidGender",  # Invalid gender
        },
        "risk_assessment": {
            "current_si": "InvalidLevel",  # Invalid SI level
            "si_intensity": 15  # Out of range
        }
    }

@pytest.fixture(scope="function")
def temp_test_directory():
    """Create temporary directory for test files"""
    temp_dir = tempfile.mkdtemp(prefix="psychiatric_test_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture(scope="function")
def mock_streamlit_session():
    """Mock Streamlit session state for testing"""
    mock_session = MagicMock()
    mock_session.patient_data = {}
    mock_session.current_section = 0
    mock_session.patient_id = "TEST-12345678"
    mock_session.assessment_start_time = datetime.now()
    mock_session.db_loaded = False
    mock_session.debug = False
    mock_session.performance_mode = True
    mock_session.auto_save_enabled = True
    mock_session.data_changed = False
    mock_session.validation_errors = []
    mock_session.validation_warnings = []
    mock_session.field_completion_status = {}
    mock_session.required_fields_met = 0
    mock_session.total_required_fields = 25
    mock_session.offline_mode = False
    mock_session.load_time_threshold = 2.0  # Add this for performance monitoring
    mock_session.last_validation = datetime.now()
    mock_session.data_validation_level = "standard"
    mock_session.assessment_flow_mode = "comprehensive"
    mock_session.render_count = 1
    mock_session.last_render_time = 0.1
    mock_session.save_in_progress = False

    return mock_session

@pytest.fixture(scope="function")
def database_error_scenarios():
    """Provide various database error scenarios for testing"""
    return {
        "connection_timeout": psycopg2.OperationalError("connection timeout"),
        "connection_refused": psycopg2.OperationalError("connection refused"),
        "invalid_credentials": psycopg2.OperationalError("authentication failed"),
        "database_not_found": psycopg2.OperationalError("database does not exist"),
        "table_not_found": psycopg2.ProgrammingError("relation does not exist"),
        "constraint_violation": psycopg2.IntegrityError("constraint violation"),
        "data_too_long": psycopg2.DataError("value too long"),
        "invalid_sql": psycopg2.ProgrammingError("syntax error"),
        "deadlock": psycopg2.OperationalError("deadlock detected"),
        "disk_full": psycopg2.OperationalError("disk full")
    }

@pytest.fixture(scope="function")
def performance_test_data():
    """Generate large dataset for performance testing"""
    generator = PsychiatricTestDataGenerator()
    test_data = []

    for i in range(100):  # Generate 100 test records
        scenario = generator.test_scenarios[i % len(generator.test_scenarios)]
        assessment = generator.generate_complete_assessment(scenario)
        assessment["patient_id"] = f"PERF-{i:08d}"
        test_data.append(assessment)

    return test_data

@pytest.fixture(scope="function")
def validation_test_cases():
    """Provide comprehensive validation test cases"""
    return {
        "valid_cases": {
            "demographics": {
                "age": 25,
                "gender": "Female",
                "marital_status": "Single, never married"
            },
            "risk_assessment": {
                "current_si": "None",
                "homicidal_ideation": "None"
            }
        },
        "invalid_cases": {
            "demographics": [
                {"age": -1, "error": "Invalid age"},
                {"age": 150, "error": "Invalid age"},
                {"gender": "InvalidGender", "error": "Invalid gender"},
                {"age": "not_a_number", "error": "Invalid type"}
            ],
            "risk_assessment": [
                {"current_si": "InvalidLevel", "error": "Invalid suicide ideation level"},
                {"si_intensity": 0, "error": "Invalid intensity range"},
                {"si_intensity": 11, "error": "Invalid intensity range"}
            ]
        },
        "edge_cases": {
            "demographics": [
                {"age": 0, "description": "Newborn age"},
                {"age": 120, "description": "Maximum valid age"},
                {"gender": "Non-binary", "description": "Non-binary gender"}
            ]
        }
    }

@pytest.fixture(scope="function")
def concurrent_access_simulation():
    """Simulate concurrent database access for testing"""
    import threading
    import time

    class ConcurrentAccessSimulator:
        def __init__(self):
            self.results = []
            self.errors = []
            self.lock = threading.Lock()

        def simulate_concurrent_saves(self, db_instance, patient_data, num_threads=5):
            """Simulate multiple threads saving data simultaneously"""
            threads = []

            def save_worker(thread_id):
                try:
                    # Modify patient_id to make each save unique
                    data = patient_data.copy()
                    data["metadata"]["patient_id"] = f"CONCURRENT-{thread_id:03d}"

                    result = db_instance.insert_patient_data(data)

                    with self.lock:
                        self.results.append((thread_id, result))

                except Exception as e:
                    with self.lock:
                        self.errors.append((thread_id, str(e)))

            # Start threads
            for i in range(num_threads):
                thread = threading.Thread(target=save_worker, args=(i,))
                threads.append(thread)
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            return self.results, self.errors

    return ConcurrentAccessSimulator()

# Test utilities
class TestUtilities:
    """Utility functions for testing"""

    @staticmethod
    def create_test_patient_data(scenario="major_depression", **overrides):
        """Create test patient data with optional overrides"""
        generator = PsychiatricTestDataGenerator()
        data = generator.generate_complete_assessment(scenario)

        # Apply overrides
        for key, value in overrides.items():
            if "." in key:
                # Handle nested keys like "demographics.age"
                keys = key.split(".")
                current = data
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            else:
                data[key] = value

        return data

    @staticmethod
    def assert_patient_data_equal(actual, expected, ignore_keys=None):
        """Assert patient data equality with optional key ignoring"""
        ignore_keys = ignore_keys or ["assessment_date", "patient_id"]

        def filter_dict(d, ignore):
            if isinstance(d, dict):
                return {k: filter_dict(v, ignore) for k, v in d.items() if k not in ignore}
            elif isinstance(d, list):
                return [filter_dict(item, ignore) for item in d]
            else:
                return d

        filtered_actual = filter_dict(actual, ignore_keys)
        filtered_expected = filter_dict(expected, ignore_keys)

        assert filtered_actual == filtered_expected

    @staticmethod
    def validate_database_schema(cursor):
        """Validate that all required database tables and columns exist"""
        required_tables = [
            "patients", "demographics", "chief_complaint", "risk_assessment",
            "mental_state_exam", "substance_use", "diagnostic_formulation",
            "treatment_planning", "audit_trail", "data_quality_metrics"
        ]

        for table in required_tables:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = %s
                )
            """, (table,))

            exists = cursor.fetchone()[0]
            assert exists, f"Required table '{table}' does not exist"

@pytest.fixture(scope="session")
def test_utilities():
    """Provide test utilities"""
    return TestUtilities