2025-08-23 22:51:38,854 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:53:31,577 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:53:52,459 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:54:26,466 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:54:43,857 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:55:01,877 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:00:52,843 - ERROR - run_enhanced_app:3511 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3465, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1162, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:01:07,806 - ERROR - run_enhanced_app:3511 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3465, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1162, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:04:19,841 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:04:41,640 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:08:07,005 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:08:34,137 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
