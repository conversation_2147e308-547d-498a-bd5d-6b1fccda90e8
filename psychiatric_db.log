2025-08-23 22:51:38,854 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:53:31,577 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:53:52,459 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:54:26,466 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:54:43,857 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 22:55:01,877 - ERROR - run_enhanced_app:3509 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3463, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1160, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 811, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:00:52,843 - ERROR - run_enhanced_app:3511 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3465, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1162, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:01:07,806 - ERROR - run_enhanced_app:3511 - Application error: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3465, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1162, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 148, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)
streamlit.errors.StreamlitDuplicateElementId: There are multiple `selectbox` elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique `key` argument to the `selectbox` element.
2025-08-23 23:04:19,841 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:04:41,640 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:08:07,005 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-23 23:08:34,137 - ERROR - run_enhanced_app:3512 - Application error: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
Traceback (most recent call last):
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 3466, in run_enhanced_app
    main()
    ~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 1163, in main
    show_enhanced_sidebar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\projects\prop streamlit 3\psychiatric_assessment_app.py", line 813, in show_enhanced_sidebar
    flow_mode = st.selectbox("Assessment Mode",
                            ["comprehensive", "quick", "focused"],
                            index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode),
                            key="assessment_flow_mode_selector")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\runtime\metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 469, in selectbox
    return self._selectbox(
           ~~~~~~~~~~~~~~~^
        label=label,
        ^^^^^^^^^^^^
    ...<13 lines>...
        ctx=ctx,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\widgets\selectbox.py", line 541, in _selectbox
    element_id = compute_and_register_element_id(
        "selectbox",
    ...<9 lines>...
        width=width,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 274, in compute_and_register_element_id
    _register_element_id(ctx, element_type_for_error, element_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\streamlit\elements\lib\utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)
streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same `key='assessment_flow_mode_selector'`. To fix this, please make sure that the `key` argument is unique for each element you create.
2025-08-24 00:37:16,195 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:37:59,250 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:39,855 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:39,910 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:39,947 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:39,973 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:39,999 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:40,037 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:40,060 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:38:40,126 - ERROR - wrapper:57 - ❌ Non-retryable error in _save_patient_data_sync: Patient data validation failed for TEST-12345678: Invalid age: -5; Invalid suicide ideation level: InvalidLevel
2025-08-24 00:38:40,142 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,779 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,811 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,853 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,890 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,929 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,946 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,980 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:10,995 - ERROR - wrapper:57 - ❌ Non-retryable error in _save_patient_data_sync: Patient data validation failed for TEST-12345678: Invalid age: -5; Invalid suicide ideation level: InvalidLevel
2025-08-24 00:39:11,046 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,273 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,301 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,376 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,395 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,526 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:11,674 - INFO - close_connections:1586 - [OK] Database connections closed cleanly
2025-08-24 00:39:14,832 - INFO - cleanup_on_exit:3414 - [OK] App cleanup completed successfully
2025-08-24 00:42:30,306 - INFO - cleanup_on_exit:3418 - [OK] App cleanup completed successfully
2025-08-24 00:43:00,928 - INFO - cleanup_on_exit:3418 - [OK] App cleanup completed successfully
2025-08-24 00:44:08,746 - INFO - close_connections:1593 - [OK] Database connections closed cleanly
2025-08-24 00:45:15,750 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:46:34,563 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,119 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,154 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,211 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,246 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,275 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,324 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,483 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,549 - ERROR - wrapper:57 - ❌ Non-retryable error in _save_patient_data_sync: Patient data validation failed for TEST-12345678: Invalid age: -5; Invalid suicide ideation level: InvalidLevel
2025-08-24 00:47:03,607 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,741 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,819 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:03,926 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:04,019 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:04,081 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:04,110 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:06,433 - INFO - cleanup_on_exit:3418 - [OK] App cleanup completed successfully
2025-08-24 00:47:49,995 - INFO - close_connections:1598 - [OK] Database connections closed cleanly
2025-08-24 00:47:51,488 - INFO - cleanup_on_exit:3418 - [OK] App cleanup completed successfully
