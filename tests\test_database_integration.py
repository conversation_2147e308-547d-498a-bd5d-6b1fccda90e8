"""
Comprehensive integration tests for database functionality.
Tests end-to-end database operations, transaction handling, and data integrity.
"""

import pytest
import os
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List
import psycopg2
from psycopg2.extras import RealDictCursor

from database import PsychiatricAssessmentDB, DatabaseError, ValidationError


@pytest.mark.integration
@pytest.mark.database
class TestDatabaseIntegration:
    """Integration tests requiring actual database connection"""

    @pytest.fixture(scope="class")
    def real_db(self, test_db_config):
        """Create real database connection for integration tests"""
        # Skip if no test database available
        try:
            # Test connection first
            conn = psycopg2.connect(**test_db_config)
            conn.close()
        except psycopg2.OperationalError:
            pytest.skip("Test database not available")

        # Set environment variables for test database
        for key, value in test_db_config.items():
            os.environ[f"DB_{key.upper()}"] = str(value)

        db = PsychiatricAssessmentDB()

        # Create tables if they don't exist
        try:
            db.create_tables()
        except Exception as e:
            pytest.skip(f"Could not create test tables: {e}")

        yield db

        # Cleanup: Remove test data
        try:
            with db.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Delete test data (patient IDs starting with TEST-)
                    cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'TEST-%'")
                    cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'PERF-%'")
                    cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'CONCURRENT-%'")
        except Exception:
            pass  # Ignore cleanup errors

    def test_end_to_end_patient_workflow(self, real_db, sample_patient_data, test_utilities):
        """Test complete patient data workflow from creation to retrieval"""
        # Prepare test data
        patient_data = {
            "metadata": {
                "patient_id": "TEST-E2E-001",
                "assessment_date": datetime.now().isoformat(),
                "duration_minutes": 45.5,
                "completion_percentage": 85.0,
                "data_version": "2.0"
            },
            "clinical_data": sample_patient_data,
            "validation_results": {
                "errors": [],
                "warnings": []
            }
        }

        # Test 1: Insert patient data
        patient_id = real_db.insert_patient_data(patient_data)
        assert patient_id == "TEST-E2E-001"

        # Test 2: Retrieve patient data
        retrieved_data = real_db.get_patient_data(patient_id)
        assert retrieved_data is not None
        assert retrieved_data["metadata"]["patient_id"] == patient_id

        # Test 3: Verify data integrity
        test_utilities.assert_patient_data_equal(
            retrieved_data["clinical_data"],
            sample_patient_data,
            ignore_keys=["assessment_date", "created_at", "updated_at"]
        )

        # Test 4: Update patient data
        updated_data = patient_data.copy()
        updated_data["clinical_data"]["demographics"]["age"] = 40
        updated_data["metadata"]["completion_percentage"] = 90.0

        updated_patient_id = real_db.insert_patient_data(updated_data)
        assert updated_patient_id == patient_id

        # Test 5: Verify update
        final_data = real_db.get_patient_data(patient_id)
        assert final_data["clinical_data"]["demographics"]["age"] == 40
        assert final_data["metadata"]["completion_percentage"] == 90.0

    def test_database_health_check_real(self, real_db):
        """Test health check with real database"""
        healthy, message = real_db.health_check()

        assert healthy is True

        # Parse health check message
        health_info = json.loads(message)
        assert health_info["status"] == "healthy"
        assert "response_time_ms" in health_info
        assert "database_size" in health_info
        assert "patient_count" in health_info
        assert health_info["response_time_ms"] < 1000  # Should be fast

    def test_transaction_rollback_on_error(self, real_db, sample_patient_data):
        """Test that transactions are properly rolled back on errors"""
        # Create valid patient data
        patient_data = {
            "metadata": {
                "patient_id": "TEST-ROLLBACK-001",
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": sample_patient_data
        }

        # Insert valid data first
        patient_id = real_db.insert_patient_data(patient_data)
        assert patient_id == "TEST-ROLLBACK-001"

        # Now try to update with invalid data that should cause rollback
        invalid_data = patient_data.copy()
        invalid_data["clinical_data"]["demographics"]["age"] = -5  # Invalid age

        with pytest.raises(ValidationError):
            real_db.insert_patient_data(invalid_data)

        # Verify original data is still intact
        retrieved_data = real_db.get_patient_data(patient_id)
        assert retrieved_data is not None
        assert retrieved_data["clinical_data"]["demographics"]["age"] != -5

    def test_concurrent_database_access(self, real_db, sample_patient_data, concurrent_access_simulation):
        """Test concurrent database access and data integrity"""
        # Prepare base patient data
        base_data = {
            "metadata": {
                "assessment_date": datetime.now().isoformat(),
                "completion_percentage": 75.0
            },
            "clinical_data": sample_patient_data
        }

        # Simulate concurrent saves
        results, errors = concurrent_access_simulation.simulate_concurrent_saves(
            real_db, base_data, num_threads=5
        )

        # Verify all saves succeeded
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 5

        # Verify all patient records were created
        for thread_id, patient_id in results:
            retrieved_data = real_db.get_patient_data(patient_id)
            assert retrieved_data is not None
            assert retrieved_data["metadata"]["patient_id"] == patient_id

    def test_data_integrity_constraints(self, real_db):
        """Test database constraints and data integrity"""
        # Test 1: Invalid patient ID format
        invalid_data = {
            "metadata": {
                "patient_id": "INVALID_FORMAT",  # Should match PSY-XXXXXXXX pattern
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": {"demographics": {"age": 25}}
        }

        with pytest.raises((ValidationError, DatabaseError)):
            real_db.insert_patient_data(invalid_data)

        # Test 2: Age constraints
        age_constraint_data = {
            "metadata": {
                "patient_id": "TEST-CONSTRAINT-001",
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": {
                "demographics": {"age": 150}  # Exceeds maximum age
            }
        }

        with pytest.raises(ValidationError):
            real_db.insert_patient_data(age_constraint_data)

        # Test 3: Required safety plan for high-risk patients
        high_risk_data = {
            "metadata": {
                "patient_id": "TEST-HIGHRISK-001",
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": {
                "risk_assessment": {
                    "current_si": "Active with plan",
                    "safety_plan_created": False  # Should require safety plan
                }
            }
        }

        with pytest.raises(ValidationError, match="Safety plan required"):
            real_db.insert_patient_data(high_risk_data)

    def test_auto_save_system_integration(self, real_db, sample_patient_data):
        """Test auto-save system with real database"""
        callback_results = []

        def test_callback(success, patient_id, error):
            callback_results.append((success, patient_id, error))

        # Queue auto-save
        patient_data = {
            "metadata": {
                "patient_id": "TEST-AUTOSAVE-001",
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": sample_patient_data
        }

        success = real_db.queue_auto_save(patient_data, test_callback, priority=1)
        assert success is True

        # Wait for auto-save to process
        time.sleep(2)

        # Verify callback was called with success
        assert len(callback_results) == 1
        success, patient_id, error = callback_results[0]
        assert success is True
        assert patient_id == "TEST-AUTOSAVE-001"
        assert error is None

        # Verify data was saved
        retrieved_data = real_db.get_patient_data("TEST-AUTOSAVE-001")
        assert retrieved_data is not None


@pytest.mark.integration
@pytest.mark.performance
@pytest.mark.slow
class TestDatabasePerformance:
    """Performance tests for database operations"""

    @pytest.fixture(scope="class")
    def perf_db(self, test_db_config):
        """Database instance for performance testing"""
        try:
            conn = psycopg2.connect(**test_db_config)
            conn.close()
        except psycopg2.OperationalError:
            pytest.skip("Test database not available for performance tests")

        for key, value in test_db_config.items():
            os.environ[f"DB_{key.upper()}"] = str(value)

        db = PsychiatricAssessmentDB()
        db.create_tables()

        yield db

        # Cleanup performance test data
        try:
            with db.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'PERF-%'")
        except Exception:
            pass

    def test_bulk_insert_performance(self, perf_db, performance_test_data):
        """Test performance of bulk insert operations"""
        start_time = time.time()

        # Insert 100 patient records
        patient_ids = []
        for patient_data in performance_test_data:
            full_data = {
                "metadata": {
                    "patient_id": patient_data["patient_id"],
                    "assessment_date": datetime.now().isoformat(),
                    "completion_percentage": 75.0
                },
                "clinical_data": patient_data
            }

            patient_id = perf_db.insert_patient_data(full_data)
            patient_ids.append(patient_id)

        end_time = time.time()
        total_time = end_time - start_time

        # Performance assertions
        assert len(patient_ids) == 100
        assert total_time < 30.0  # Should complete within 30 seconds

        avg_time_per_insert = total_time / 100
        assert avg_time_per_insert < 0.5  # Each insert should take less than 500ms

        print(f"Bulk insert performance: {total_time:.2f}s total, {avg_time_per_insert:.3f}s per record")

    def test_bulk_retrieval_performance(self, perf_db, performance_test_data):
        """Test performance of bulk retrieval operations"""
        # First, insert test data
        patient_ids = []
        for i, patient_data in enumerate(performance_test_data[:50]):  # Use 50 records
            full_data = {
                "metadata": {
                    "patient_id": f"PERF-RETRIEVE-{i:03d}",
                    "assessment_date": datetime.now().isoformat()
                },
                "clinical_data": patient_data
            }
            patient_id = perf_db.insert_patient_data(full_data)
            patient_ids.append(patient_id)

        # Test retrieval performance
        start_time = time.time()

        retrieved_count = 0
        for patient_id in patient_ids:
            data = perf_db.get_patient_data(patient_id)
            if data:
                retrieved_count += 1

        end_time = time.time()
        total_time = end_time - start_time

        # Performance assertions
        assert retrieved_count == len(patient_ids)
        assert total_time < 10.0  # Should complete within 10 seconds

        avg_time_per_retrieval = total_time / len(patient_ids)
        assert avg_time_per_retrieval < 0.2  # Each retrieval should take less than 200ms

        print(f"Bulk retrieval performance: {total_time:.2f}s total, {avg_time_per_retrieval:.3f}s per record")

    def test_concurrent_write_performance(self, perf_db, sample_patient_data):
        """Test performance under concurrent write load"""
        num_threads = 10
        records_per_thread = 5
        results = []
        errors = []

        def write_worker(thread_id):
            thread_results = []
            thread_errors = []

            for i in range(records_per_thread):
                try:
                    patient_data = {
                        "metadata": {
                            "patient_id": f"PERF-CONCURRENT-{thread_id:02d}-{i:02d}",
                            "assessment_date": datetime.now().isoformat()
                        },
                        "clinical_data": sample_patient_data
                    }

                    start_time = time.time()
                    patient_id = perf_db.insert_patient_data(patient_data)
                    end_time = time.time()

                    thread_results.append((patient_id, end_time - start_time))

                except Exception as e:
                    thread_errors.append((thread_id, i, str(e)))

            results.extend(thread_results)
            errors.extend(thread_errors)

        # Start concurrent writes
        start_time = time.time()
        threads = []

        for thread_id in range(num_threads):
            thread = threading.Thread(target=write_worker, args=(thread_id,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time = end_time - start_time

        # Performance assertions
        assert len(errors) == 0, f"Concurrent write errors: {errors}"
        assert len(results) == num_threads * records_per_thread
        assert total_time < 20.0  # Should complete within 20 seconds

        # Check individual operation times
        operation_times = [op_time for _, op_time in results]
        avg_operation_time = sum(operation_times) / len(operation_times)
        max_operation_time = max(operation_times)

        assert avg_operation_time < 1.0  # Average operation should be under 1 second
        assert max_operation_time < 3.0  # No single operation should take more than 3 seconds

        print(f"Concurrent write performance: {total_time:.2f}s total, "
              f"{avg_operation_time:.3f}s avg, {max_operation_time:.3f}s max")

    def test_connection_pool_performance(self, perf_db):
        """Test connection pool performance under load"""
        num_operations = 100
        start_time = time.time()

        successful_operations = 0

        for i in range(num_operations):
            try:
                # Test health check as a simple database operation
                healthy, _ = perf_db.health_check()
                if healthy:
                    successful_operations += 1
            except Exception:
                pass

        end_time = time.time()
        total_time = end_time - start_time

        # Performance assertions
        assert successful_operations >= num_operations * 0.95  # At least 95% success rate
        assert total_time < 10.0  # Should complete within 10 seconds

        avg_time_per_operation = total_time / num_operations
        assert avg_time_per_operation < 0.1  # Each operation should be under 100ms

        print(f"Connection pool performance: {successful_operations}/{num_operations} successful, "
              f"{total_time:.2f}s total, {avg_time_per_operation:.3f}s avg")


@pytest.mark.integration
@pytest.mark.database
class TestDataQualityAndAudit:
    """Test data quality monitoring and audit trail functionality"""

    @pytest.fixture(scope="class")
    def audit_db(self, test_db_config):
        """Database instance for audit testing"""
        try:
            conn = psycopg2.connect(**test_db_config)
            conn.close()
        except psycopg2.OperationalError:
            pytest.skip("Test database not available for audit tests")

        for key, value in test_db_config.items():
            os.environ[f"DB_{key.upper()}"] = str(value)

        db = PsychiatricAssessmentDB()
        db.create_tables()

        yield db

        # Cleanup audit test data
        try:
            with db.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'AUDIT-%'")
                    cursor.execute("DELETE FROM audit_trail WHERE patient_id LIKE 'AUDIT-%'")
        except Exception:
            pass

    def test_audit_trail_creation(self, audit_db, sample_patient_data):
        """Test that audit trail entries are created for data changes"""
        patient_data = {
            "metadata": {
                "patient_id": "AUDIT-TRAIL-001",
                "assessment_date": datetime.now().isoformat()
            },
            "clinical_data": sample_patient_data
        }

        # Insert patient data
        patient_id = audit_db.insert_patient_data(patient_data)

        # Check audit trail
        with audit_db.get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT operation, table_name, patient_id, change_timestamp
                    FROM audit_trail
                    WHERE patient_id = %s
                    ORDER BY change_timestamp DESC
                """, (patient_id,))

                audit_entries = cursor.fetchall()

        # Should have at least one audit entry for the insert
        assert len(audit_entries) > 0

        # Check the most recent entry
        latest_entry = audit_entries[0]
        assert latest_entry['operation'] in ['INSERT', 'UPDATE']
        assert latest_entry['patient_id'] == patient_id

    def test_data_quality_metrics_calculation(self, audit_db, sample_patient_data):
        """Test data quality metrics calculation and storage"""
        # Create patient with varying data completeness
        incomplete_data = {
            "demographics": {"age": 30},  # Missing many fields
            "chief_complaint": {"complaint": "Test complaint"}  # Minimal data
        }

        patient_data = {
            "metadata": {
                "patient_id": "AUDIT-QUALITY-001",
                "assessment_date": datetime.now().isoformat(),
                "completion_percentage": 25.0  # Low completion
            },
            "clinical_data": incomplete_data,
            "validation_results": {
                "errors": ["Missing required field: gender"],
                "warnings": ["Incomplete demographics section"]
            }
        }

        patient_id = audit_db.insert_patient_data(patient_data)

        # Check data quality metrics
        with audit_db.get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT completeness_score, validation_errors, validation_warnings
                    FROM data_quality_metrics
                    WHERE patient_id = %s
                    ORDER BY calculated_at DESC
                    LIMIT 1
                """, (patient_id,))

                quality_metrics = cursor.fetchone()

        if quality_metrics:  # May not be implemented yet
            assert quality_metrics['completeness_score'] <= 50.0  # Should reflect low completion
            assert len(quality_metrics['validation_errors']) > 0