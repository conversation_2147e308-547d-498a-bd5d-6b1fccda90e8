"""
Comprehensive unit tests for database operations.
Tests all CRUD operations, connection handling, validation, and error scenarios.
"""

import pytest
import json
import uuid
from unittest.mock import Mock, MagicMock, patch, call
from datetime import datetime, timedelta
import psycopg2
from psycopg2.extras import RealDictCursor

from database import PsychiatricAssessmentDB, DatabaseError, ValidationError


class TestDatabaseConnection:
    """Test database connection and pool management"""

    @pytest.mark.unit
    def test_database_initialization_success(self, mock_db_pool):
        """Test successful database initialization"""
        with patch('database.psycopg2.pool.ThreadedConnectionPool') as mock_pool_class:
            mock_pool_class.return_value = mock_db_pool

            db = PsychiatricAssessmentDB()

            assert db.pool is not None
            assert db.connection_timeout == 30
            assert db.retry_attempts == 3
            mock_pool_class.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_database_initialization_failure(self):
        """Test database initialization failure handling"""
        with patch('database.psycopg2.pool.ThreadedConnectionPool') as mock_pool_class:
            mock_pool_class.side_effect = psycopg2.OperationalError("Connection failed")

            db = PsychiatricAssessmentDB()

            assert db.pool is None
            assert db.connection_metrics['failed_connections'] > 0

    @pytest.mark.unit
    def test_get_db_connection_success(self, mock_psychiatric_db, mock_db_connection):
        """Test successful database connection retrieval"""
        mock_conn, mock_cursor = mock_db_connection
        mock_psychiatric_db.pool.getconn.return_value = mock_conn

        with mock_psychiatric_db.get_db_connection() as conn:
            assert conn is not None
            mock_psychiatric_db.pool.getconn.assert_called_once()
            mock_psychiatric_db.pool.putconn.assert_not_called()

        mock_psychiatric_db.pool.putconn.assert_called_once_with(mock_conn)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_get_db_connection_closed_connection(self, mock_psychiatric_db, mock_db_connection):
        """Test handling of closed database connections"""
        mock_conn, mock_cursor = mock_db_connection
        mock_conn.closed = 1  # Simulate closed connection

        mock_psychiatric_db.pool.getconn.side_effect = [mock_conn, mock_conn]

        with mock_psychiatric_db.get_db_connection() as conn:
            assert conn is not None

        # Should call getconn twice due to closed connection
        assert mock_psychiatric_db.pool.getconn.call_count == 2

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_get_db_connection_operational_error(self, mock_psychiatric_db):
        """Test handling of operational errors during connection"""
        mock_psychiatric_db.pool.getconn.side_effect = psycopg2.OperationalError("Connection timeout")

        with pytest.raises(DatabaseError, match="Database connection failed"):
            with mock_psychiatric_db.get_db_connection():
                pass

    @pytest.mark.unit
    def test_health_check_success(self, mock_psychiatric_db, mock_db_connection):
        """Test successful health check"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.fetchone.side_effect = [
            {'test': 1},  # First call for basic test
            {
                'db_size': '10 MB',
                'patient_count': 5,
                'db_version': 'PostgreSQL 13.0'
            }  # Second call for stats
        ]

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            healthy, message = mock_psychiatric_db.health_check()

            assert healthy is True
            assert "healthy" in message
            mock_cursor.execute.assert_called()

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_health_check_failure(self, mock_psychiatric_db):
        """Test health check failure"""
        mock_psychiatric_db.pool = None

        healthy, message = mock_psychiatric_db.health_check()

        assert healthy is False
        assert "Connection pool not initialized" in message

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_health_check_database_error(self, mock_psychiatric_db, mock_db_connection):
        """Test health check with database error"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.OperationalError("Database error")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            healthy, message = mock_psychiatric_db.health_check()

            assert healthy is False
            assert "Health check failed" in message


class TestDataValidation:
    """Test data validation functionality"""

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_success(self, mock_psychiatric_db, sample_demographics_data, sample_risk_assessment_data):
        """Test successful patient data validation"""
        clinical_data = {
            "demographics": sample_demographics_data,
            "risk_assessment": sample_risk_assessment_data
        }

        # Should not raise any exception
        mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_invalid_age(self, mock_psychiatric_db):
        """Test validation failure for invalid age"""
        clinical_data = {
            "demographics": {"age": -5}
        }

        with pytest.raises(ValidationError, match="Invalid age"):
            mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_invalid_gender(self, mock_psychiatric_db):
        """Test validation failure for invalid gender"""
        clinical_data = {
            "demographics": {"gender": "InvalidGender"}
        }

        with pytest.raises(ValidationError, match="Invalid gender"):
            mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_invalid_suicide_ideation(self, mock_psychiatric_db):
        """Test validation failure for invalid suicide ideation level"""
        clinical_data = {
            "risk_assessment": {"current_si": "InvalidLevel"}
        }

        with pytest.raises(ValidationError, match="Invalid suicide ideation level"):
            mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_safety_plan_required(self, mock_psychiatric_db):
        """Test validation failure when safety plan is required but missing"""
        clinical_data = {
            "risk_assessment": {
                "current_si": "Active with plan",
                "safety_plan_created": False
            }
        }

        with pytest.raises(ValidationError, match="Safety plan required"):
            mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    @pytest.mark.validation
    def test_validate_patient_data_empty_primary_diagnosis(self, mock_psychiatric_db):
        """Test validation failure for empty primary diagnosis"""
        clinical_data = {
            "diagnostic_formulation": {"primary_diagnosis": "   "}
        }

        with pytest.raises(ValidationError, match="Primary diagnosis cannot be empty"):
            mock_psychiatric_db._validate_patient_data(clinical_data, "TEST-12345678")

    @pytest.mark.unit
    def test_calculate_data_checksum(self, mock_psychiatric_db):
        """Test data checksum calculation"""
        test_data = {"test": "data", "number": 123}

        checksum1 = mock_psychiatric_db._calculate_data_checksum(test_data)
        checksum2 = mock_psychiatric_db._calculate_data_checksum(test_data)

        assert checksum1 == checksum2
        assert len(checksum1) == 64  # SHA256 hex digest length

        # Different data should produce different checksum
        different_data = {"test": "different", "number": 456}
        checksum3 = mock_psychiatric_db._calculate_data_checksum(different_data)
        assert checksum1 != checksum3


class TestCRUDOperations:
    """Test Create, Read, Update, Delete operations"""

    @pytest.mark.unit
    def test_save_patient_data_success(self, mock_psychiatric_db, mock_db_connection, sample_patient_data):
        """Test successful patient data save"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.fetchone.return_value = None  # No existing record

        # Prepare test data
        patient_data = {
            "metadata": {
                "patient_id": "TEST-12345678",
                "assessment_date": datetime.now().isoformat(),
                "completion_percentage": 75.0
            },
            "clinical_data": sample_patient_data
        }

        result = mock_psychiatric_db._save_patient_data_sync(mock_cursor, patient_data)

        assert result == "TEST-12345678"
        assert mock_cursor.execute.call_count >= 2  # At least main insert + one clinical table

    @pytest.mark.unit
    def test_save_patient_data_update_existing(self, mock_psychiatric_db, mock_db_connection, sample_patient_data):
        """Test updating existing patient data"""
        mock_conn, mock_cursor = mock_db_connection

        # Simulate existing record
        mock_cursor.fetchone.return_value = {
            'data_checksum': 'old_checksum',
            'updated_at': datetime.now()
        }

        patient_data = {
            "metadata": {
                "patient_id": "TEST-12345678",
                "assessment_date": datetime.now().isoformat(),
                "completion_percentage": 85.0
            },
            "clinical_data": sample_patient_data
        }

        result = mock_psychiatric_db._save_patient_data_sync(mock_cursor, patient_data)

        assert result == "TEST-12345678"
        # Should execute update queries
        assert mock_cursor.execute.call_count >= 2

    @pytest.mark.unit
    def test_save_patient_data_no_changes(self, mock_psychiatric_db, mock_db_connection, sample_patient_data):
        """Test saving when no changes detected"""
        mock_conn, mock_cursor = mock_db_connection

        patient_data = {
            "metadata": {"patient_id": "TEST-12345678"},
            "clinical_data": sample_patient_data
        }

        # Calculate checksum for the data
        checksum = mock_psychiatric_db._calculate_data_checksum(patient_data)

        # Simulate existing record with same checksum
        mock_cursor.fetchone.return_value = {
            'data_checksum': checksum,
            'updated_at': datetime.now()
        }

        result = mock_psychiatric_db._save_patient_data_sync(mock_cursor, patient_data)

        assert result == "TEST-12345678"
        # Should only execute the checksum query, not save queries
        assert mock_cursor.execute.call_count == 1

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_save_patient_data_validation_error(self, mock_psychiatric_db, mock_db_connection):
        """Test save with validation errors"""
        mock_conn, mock_cursor = mock_db_connection

        invalid_data = {
            "metadata": {"patient_id": "TEST-12345678"},
            "clinical_data": {
                "demographics": {"age": -5},  # Invalid age
                "risk_assessment": {"current_si": "InvalidLevel"}  # Invalid SI level
            }
        }

        with pytest.raises(ValidationError):
            mock_psychiatric_db._save_patient_data_sync(mock_cursor, invalid_data)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_save_patient_data_invalid_format(self, mock_psychiatric_db, mock_db_connection):
        """Test save with invalid data format"""
        mock_conn, mock_cursor = mock_db_connection

        with pytest.raises(ValidationError, match="Invalid patient data format"):
            mock_psychiatric_db._save_patient_data_sync(mock_cursor, "not_a_dict")

    @pytest.mark.unit
    def test_get_patient_data_success(self, mock_psychiatric_db, mock_db_connection):
        """Test successful patient data retrieval"""
        mock_conn, mock_cursor = mock_db_connection

        # Mock return data
        mock_cursor.fetchone.return_value = {
            'patient_data': json.dumps({
                "metadata": {"patient_id": "TEST-12345678"},
                "clinical_data": {"demographics": {"age": 35}}
            })
        }

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            result = mock_psychiatric_db.get_patient_data("TEST-12345678")

            assert result is not None
            assert "metadata" in result
            assert "clinical_data" in result

    @pytest.mark.unit
    def test_get_patient_data_not_found(self, mock_psychiatric_db, mock_db_connection):
        """Test patient data retrieval when patient not found"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.fetchone.return_value = None

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            result = mock_psychiatric_db.get_patient_data("NONEXISTENT-ID")

            assert result is None

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_get_patient_data_database_error(self, mock_psychiatric_db, mock_db_connection):
        """Test patient data retrieval with database error"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.OperationalError("Database error")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with pytest.raises(DatabaseError):
                mock_psychiatric_db.get_patient_data("TEST-12345678")

    @pytest.mark.unit
    def test_insert_patient_data_success(self, mock_psychiatric_db, sample_patient_data):
        """Test successful patient data insertion"""
        with patch.object(mock_psychiatric_db, '_save_patient_data_sync') as mock_save:
            mock_save.return_value = "TEST-12345678"

            with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
                mock_conn = MagicMock()
                mock_cursor = MagicMock()
                mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
                mock_get_conn.return_value.__enter__.return_value = mock_conn

                patient_data = {
                    "metadata": {"patient_id": "TEST-12345678"},
                    "clinical_data": sample_patient_data
                }

                result = mock_psychiatric_db.insert_patient_data(patient_data)

                assert result == "TEST-12345678"
                mock_save.assert_called_once_with(mock_cursor, patient_data)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_insert_patient_data_connection_error(self, mock_psychiatric_db, sample_patient_data):
        """Test patient data insertion with connection error"""
        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = DatabaseError("Connection failed")

            patient_data = {
                "metadata": {"patient_id": "TEST-12345678"},
                "clinical_data": sample_patient_data
            }

            with pytest.raises(DatabaseError):
                mock_psychiatric_db.insert_patient_data(patient_data)


class TestAutoSaveSystem:
    """Test auto-save functionality and queuing system"""

    @pytest.mark.unit
    def test_queue_auto_save_success(self, mock_psychiatric_db, sample_patient_data):
        """Test successful auto-save queuing"""
        callback = MagicMock()

        result = mock_psychiatric_db.queue_auto_save(sample_patient_data, callback, priority=1)

        assert result is True
        assert mock_psychiatric_db.auto_save_queue.qsize() == 1

    @pytest.mark.unit
    def test_queue_auto_save_queue_full(self, mock_psychiatric_db, sample_patient_data):
        """Test auto-save when queue is full"""
        callback = MagicMock()

        # Fill the queue to capacity
        for i in range(50):
            mock_psychiatric_db.queue_auto_save(sample_patient_data, None, priority=1)

        # This should fail due to full queue
        result = mock_psychiatric_db.queue_auto_save(sample_patient_data, callback, priority=1)

        assert result is False
        callback.assert_called_once_with(False, None, "Queue full")

    @pytest.mark.unit
    def test_queue_auto_save_no_callback(self, mock_psychiatric_db, sample_patient_data):
        """Test auto-save queuing without callback"""
        result = mock_psychiatric_db.queue_auto_save(sample_patient_data, None, priority=1)

        assert result is True
        assert mock_psychiatric_db.auto_save_queue.qsize() == 1

    @pytest.mark.unit
    def test_process_save_batch_success(self, mock_psychiatric_db, mock_db_connection, sample_patient_data):
        """Test successful batch processing"""
        mock_conn, mock_cursor = mock_db_connection

        # Create test batch
        callback1 = MagicMock()
        callback2 = MagicMock()
        batch = [
            (sample_patient_data, callback1),
            (sample_patient_data, callback2)
        ]

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with patch.object(mock_psychiatric_db, '_save_patient_data_sync') as mock_save:
                mock_save.return_value = "TEST-12345678"

                mock_psychiatric_db._process_save_batch(batch)

                # Both callbacks should be called with success
                callback1.assert_called_once_with(True, "TEST-12345678", None)
                callback2.assert_called_once_with(True, "TEST-12345678", None)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_process_save_batch_partial_failure(self, mock_psychiatric_db, mock_db_connection, sample_patient_data):
        """Test batch processing with partial failures"""
        mock_conn, mock_cursor = mock_db_connection

        callback1 = MagicMock()
        callback2 = MagicMock()
        batch = [
            (sample_patient_data, callback1),
            (sample_patient_data, callback2)
        ]

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with patch.object(mock_psychiatric_db, '_save_patient_data_sync') as mock_save:
                # First save succeeds, second fails
                mock_save.side_effect = ["TEST-12345678", Exception("Save failed")]

                mock_psychiatric_db._process_save_batch(batch)

                # First callback should succeed, second should fail
                callback1.assert_called_once_with(True, "TEST-12345678", None)
                callback2.assert_called_once_with(False, None, "Save failed")

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_process_save_batch_connection_failure(self, mock_psychiatric_db, sample_patient_data):
        """Test batch processing with connection failure"""
        callback = MagicMock()
        batch = [(sample_patient_data, callback)]

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.side_effect = DatabaseError("Connection failed")

            mock_psychiatric_db._process_save_batch(batch)

            # Callback should be called with failure
            callback.assert_called_once_with(False, None, "Connection failed")


class TestRetryMechanism:
    """Test retry functionality for database operations"""

    @pytest.mark.unit
    def test_retry_decorator_success_first_attempt(self, mock_psychiatric_db, mock_db_connection):
        """Test retry decorator when operation succeeds on first attempt"""
        mock_conn, mock_cursor = mock_db_connection

        @mock_psychiatric_db.retry_on_failure(max_retries=3)
        def test_operation():
            return "success"

        result = test_operation()
        assert result == "success"

    @pytest.mark.unit
    def test_retry_decorator_success_after_retries(self, mock_psychiatric_db):
        """Test retry decorator when operation succeeds after retries"""
        call_count = 0

        @mock_psychiatric_db.retry_on_failure(max_retries=3, delay=0.1)
        def test_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise psycopg2.OperationalError("Temporary failure")
            return "success"

        result = test_operation()
        assert result == "success"
        assert call_count == 3

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_retry_decorator_max_retries_exceeded(self, mock_psychiatric_db):
        """Test retry decorator when max retries are exceeded"""
        @mock_psychiatric_db.retry_on_failure(max_retries=2, delay=0.1)
        def test_operation():
            raise psycopg2.OperationalError("Persistent failure")

        with pytest.raises(DatabaseError, match="Database operation failed after 2 retries"):
            test_operation()

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_retry_decorator_non_retryable_error(self, mock_psychiatric_db):
        """Test retry decorator with non-retryable error"""
        @mock_psychiatric_db.retry_on_failure(max_retries=3)
        def test_operation():
            raise ValueError("Non-retryable error")

        with pytest.raises(ValueError, match="Non-retryable error"):
            test_operation()


class TestTableCreation:
    """Test database schema creation and management"""

    @pytest.mark.unit
    def test_create_tables_success(self, mock_psychiatric_db, mock_db_connection):
        """Test successful table creation"""
        mock_conn, mock_cursor = mock_db_connection

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            # Should not raise any exception
            mock_psychiatric_db.create_tables()

            # Should execute multiple CREATE TABLE statements
            assert mock_cursor.execute.call_count > 10

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_create_tables_database_error(self, mock_psychiatric_db, mock_db_connection):
        """Test table creation with database error"""
        mock_conn, mock_cursor = mock_db_connection
        mock_cursor.execute.side_effect = psycopg2.ProgrammingError("Permission denied")

        with patch.object(mock_psychiatric_db, 'get_db_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_conn

            with pytest.raises(Exception):
                mock_psychiatric_db.create_tables()

    @pytest.mark.unit
    def test_create_performance_indexes(self, mock_psychiatric_db, mock_db_connection):
        """Test performance index creation"""
        mock_conn, mock_cursor = mock_db_connection

        # Should not raise exception even if some indexes fail
        mock_psychiatric_db._create_performance_indexes(mock_cursor)

        # Should attempt to create multiple indexes
        assert mock_cursor.execute.call_count > 15